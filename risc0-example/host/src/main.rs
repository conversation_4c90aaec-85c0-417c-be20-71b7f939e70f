use risc0_zkvm::{default_prover, ExecutorEnv};
use serde::{Deserialize, Serialize};
use std::io::{self, Read};
use std::time::Instant;
use methods::{METHOD_ELF, METHOD_ID};
use base64::{engine::general_purpose, Engine as _};

#[derive(Deserialize, Serialize)]
struct JobInput {
    job_type: String,
    params: serde_json::Value,
}

fn main() {
    // Đọc input từ stdin
    let mut buffer = String::new();
    io::stdin().read_to_string(&mut buffer).unwrap();
    eprintln!("📥 Received input: {}", buffer);

    let job_input: JobInput = match serde_json::from_str(&buffer) {
        Ok(parsed) => parsed,
        Err(e) => {
            eprintln!("❌ Failed to parse input JSON: {}", e);
            std::process::exit(1);
        }
    };

    // Lấy params
    let numbers_val = &job_input.params["numbers"];
    if !numbers_val.is_array() {
        eprintln!("❌ Invalid or missing 'numbers' array in params: {:?}", numbers_val);
        std::process::exit(1);
    }

    let numbers: Vec<u32> = match serde_json::from_value(numbers_val.clone()) {
        Ok(n) => n,
        Err(e) => {
            eprintln!("❌ Failed to deserialize numbers: {}", e);
            std::process::exit(1);
        }
    };

    eprintln!("🔢 Numbers to sum: {:?}", numbers);

    let env = ExecutorEnv::builder()
        .write(&numbers)
        .unwrap()
        .build()
        .unwrap();

    // Gọi prover
    let start = Instant::now();
    let receipt_info = match default_prover().prove(env, &METHOD_ELF) {
        Ok(r) => r,
        Err(e) => {
            eprintln!("❌ Failed to generate proof: {}", e);
            std::process::exit(1);
        }
    };
    let duration = start.elapsed();
    eprintln!("⏱️ Generated receipt in {:?}", duration);

    // Verify lại proof (có thể bỏ nếu không cần)
    receipt_info.receipt.verify(METHOD_ID).unwrap();

    // Giải mã journal (output của guest)
    let sum: u32 = receipt_info.receipt.journal.decode().unwrap();
    let journal_data = serde_json::json!(sum);

    // Lấy raw seal bytes từ receipt (v2.x API)
    let seal_bytes = receipt_info
        .receipt
        .inner
        .groth16()
        .expect("No groth16 data in receipt")
        .seal
        .clone();

    // Gói kết quả JSON trả về (seal base64, journal, guest code id)
    let guest_code_bytes: Vec<u8> = METHOD_ID
        .iter()
        .flat_map(|x| x.to_le_bytes())
        .collect();

    let seal_b64 = general_purpose::STANDARD.encode(&seal_bytes);
    let guest_id_b64 = general_purpose::STANDARD.encode(&guest_code_bytes);

    let output = serde_json::json!({
        "seal_base64": seal_b64,
        "journal_data": journal_data,
        "guest_code_id": guest_id_b64,
    });

    println!("{}", output.to_string());
}
