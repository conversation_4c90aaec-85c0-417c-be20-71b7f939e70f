require('dotenv').config();
require("@nomicfoundation/hardhat-toolbox");

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  solidity: {
    version: "0.8.28",
    settings: {
      optimizer: { enabled: true, runs: 200 },
      viaIR: true
    }
  },
  networks: {
    ethermint: {
      url: process.env.RPC_URL, // Ethermint JSON-RPC endpoint
      chainId: parseInt(process.env.CHAIN_ID), // Chain ID từ init.sh
      accounts: [
        process.env.BACKEND_PRIVATE_KEY
      ],
      gas: ********,
      gasPrice: **********
    },
    localhost: {
      url: "http://127.0.0.1:9545",
      chainId: 31337
    }
  }
};

