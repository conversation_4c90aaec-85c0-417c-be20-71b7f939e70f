const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const fetch = require('node-fetch');

class IPFSUploader {
  constructor() {
    // Always use local IPFS node
    this.ipfsEndpoint = 'http://localhost:5001';
    this.useLocalNode = true;
    console.log('🔧 Using local IPFS node at http://localhost:5001');
  }

  /**
   * Upload job artifact folder to IPFS
   */
  async uploadJobArtifact(jobId, jobDataDir) {
    console.log(`📤 Uploading job ${jobId} artifact to IPFS...`);
    
    try {
      // Create artifact package
      const artifactData = await this.createArtifactPackage(jobId, jobDataDir);
      
      // Upload to local IPFS node
      const cid = await this.uploadToLocalIPFS(artifactData);
      
      // Save CID to job directory
      const cidFilePath = path.join(jobDataDir, 'ipfs_cid.txt');
      fs.writeFileSync(cidFilePath, cid);
      
      console.log(`✅ Job ${jobId} artifact uploaded to IPFS`);
      console.log(`🔗 IPFS CID: ${cid}`);
      console.log(`📁 CID saved to: ${cidFilePath}`);
      
      return {
        cid: cid,
        ipfsUrl: `https://ipfs.io/ipfs/${cid}`,
        gatewayUrl: `https://gateway.pinata.cloud/ipfs/${cid}`,
        localPath: cidFilePath
      };
      
    } catch (error) {
      console.error(`❌ Failed to upload job ${jobId} to IPFS:`, error.message);
      throw error;
    }
  }

  /**
   * Create artifact package with all necessary files
   */
  async createArtifactPackage(jobId, jobDataDir) {
    const requiredFiles = [
      'output.json',
      'stdout.log', 
      'stderr.log',
      'result.hash',
      'signature.txt',
      'metadata.json'
    ];

    const artifactData = {
      jobId: jobId,
      timestamp: new Date().toISOString(),
      files: {},
      manifest: {
        version: '1.0',
        jobId: jobId,
        createdAt: new Date().toISOString(),
        fileCount: 0,
        totalSize: 0
      }
    };

    let totalSize = 0;
    let fileCount = 0;

    for (const fileName of requiredFiles) {
      const filePath = path.join(jobDataDir, fileName);
      
      if (fs.existsSync(filePath)) {
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const fileSize = fs.statSync(filePath).size;
        
        artifactData.files[fileName] = {
          content: fileContent,
          size: fileSize,
          encoding: 'utf8'
        };
        
        totalSize += fileSize;
        fileCount++;
        
        console.log(`📄 Packaged ${fileName} (${fileSize} bytes)`);
      } else {
        console.log(`⚠️ File ${fileName} not found, skipping...`);
      }
    }

    artifactData.manifest.fileCount = fileCount;
    artifactData.manifest.totalSize = totalSize;

    console.log(`📦 Artifact package created: ${fileCount} files, ${totalSize} bytes`);
    return artifactData;
  }



  /**
   * Upload to local IPFS node
   */
  async uploadToLocalIPFS(artifactData) {
    const url = `${this.ipfsEndpoint}/api/v0/add`;
    
    const formData = new FormData();
    formData.append('file', JSON.stringify(artifactData, null, 2), {
      filename: `job-${artifactData.jobId}-artifact.json`,
      contentType: 'application/json'
    });

    const response = await fetch(url, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Local IPFS upload failed: ${response.status} ${errorText}`);
    }

    const result = await response.json();
    return result.Hash;
  }

  /**
   * Verify IPFS upload by trying to retrieve it
   */
  async verifyUpload(cid) {
    console.log(`🔍 Verifying IPFS upload: ${cid}`);
    
    try {
      const gatewayUrl = `https://ipfs.io/ipfs/${cid}`;
      const response = await fetch(gatewayUrl, { 
        method: 'HEAD',
        timeout: 10000 
      });
      
      const isAccessible = response.ok;
      console.log(`${isAccessible ? '✅' : '❌'} IPFS verification: ${isAccessible ? 'SUCCESS' : 'FAILED'}`);
      
      return {
        accessible: isAccessible,
        url: gatewayUrl,
        status: response.status
      };
      
    } catch (error) {
      console.log(`⚠️ IPFS verification failed: ${error.message}`);
      return {
        accessible: false,
        error: error.message
      };
    }
  }

  /**
   * Download and verify artifact from IPFS
   */
  async downloadArtifact(cid) {
    console.log(`📥 Downloading artifact from IPFS: ${cid}`);

    try {
      // Try local IPFS node first
      let artifactData;
      try {
        const localUrl = `${this.ipfsEndpoint}/api/v0/cat?arg=${cid}`;
        const response = await fetch(localUrl, { method: 'POST' });

        if (response.ok) {
          artifactData = await response.json();
          console.log(`✅ Downloaded from local IPFS node`);
        } else {
          throw new Error(`Local IPFS failed: ${response.status}`);
        }
      } catch (localError) {
        console.log(`⚠️ Local IPFS failed, trying gateway: ${localError.message}`);

        // Fallback to public gateway
        const gatewayUrl = `https://ipfs.io/ipfs/${cid}`;
        const response = await fetch(gatewayUrl, { timeout: 10000 });

        if (!response.ok) {
          throw new Error(`Gateway download failed: ${response.status}`);
        }

        artifactData = await response.json();
        console.log(`✅ Downloaded from public gateway`);
      }

      console.log(`✅ Downloaded artifact for job ${artifactData.jobId}`);
      console.log(`📊 Files: ${artifactData.manifest.fileCount}, Size: ${artifactData.manifest.totalSize} bytes`);

      return artifactData;

    } catch (error) {
      console.error(`❌ Failed to download artifact:`, error.message);
      throw error;
    }
  }
}

module.exports = IPFSUploader;