# DATACOIN Docker Sandbox

Hệ thống Docker sandbox để chạy các job types một cách an toàn và cô lập.

## C<PERSON>u trúc thư mục

```
backend/
├── docker/                    # Docker configurations
│   ├── data_processing/       # Data processing job container
│   │   ├── Dockerfile
│   │   ├── package.json
│   │   └── process.js
│   └── ai_training/          # AI training job container
│       ├── Dockerfile
│       ├── requirements.txt
│       └── process.py
├── job-data/                 # Job execution data
│   └── {jobId}/             # Per-job directory
│       ├── input.json       # Job input
│       ├── output.json      # Job result
│       ├── stdout.log       # Standard output
│       ├── stderr.log       # Error output
│       ├── result.hash      # SHA256 hash of results
│       ├── signature.txt    # ECDSA signature of hash
│       ├── metadata.json    # File metadata
│       └── combined_data.txt # Combined data for hashing
├── docker-utils.js          # Docker management utilities
├── job-listener.js          # Main job processor
├── result-authenticator.js  # Job result authentication
├── checker-verify.js        # Result verification for checkers
└── build-images.sh          # Build script
```

## Supported Job Types

### 1. data_processing
- **Container**: Node.js 18 Alpine
- **Function**: <PERSON>ử lý dữ liệu số, tính toán thống kê
- **Input**: `{ "numbers": [1, 2, 3, 4, 5] }`
- **Output**: Sum, average, min, max, count

### 2. ai_training
- **Container**: Python 3.11 Alpine
- **Function**: Mô phỏng training AI model
- **Input**: `{ "dataset_size": 1000, "epochs": 10, "model_type": "linear_regression" }`
- **Output**: Training metrics, model parameters

## Cách sử dụng

### 1. Build Docker Images

```bash
cd erc-20/backend
./build-images.sh
```

### 2. Chạy Job Processor

```bash
npm start
# hoặc
node job-listener.js
```

### 3. Job Execution Flow

1. **Job Request**: Smart contract tạo job mới
2. **Job Assignment**: Provider assign job
3. **Container Creation**: Tạo container với security restrictions
4. **Job Processing**: Container đọc input.json, xử lý, ghi output.json
5. **Result Collection**: Thu thập kết quả từ job-data/{jobId}
6. **Result Authentication**: Tạo hash và ký số kết quả
7. **Cleanup**: Container tự động xóa (--rm)

## Security Features

### Container Security
- `--read-only`: Filesystem chỉ đọc
- `--network=none`: Không có network access
- `--memory=512m`: Giới hạn memory
- `--cpus=1`: Giới hạn CPU
- Non-root user trong container

### Resource Limits
- Memory: 512MB
- CPU: 1 core
- Timeout: 5 phút
- No network access

### Data Isolation
- Mỗi job có thư mục riêng
- Input mount read-only
- Output mount write-only
- Logs được lưu riêng biệt

### Result Authentication
- **Hash Creation**: SHA256 hash của output.json + logs + metadata
- **Digital Signature**: ECDSA signature bằng provider private key
- **Verification**: Checker có thể verify tính toàn vẹn và xác thực
- **Audit Trail**: Đầy đủ thông tin để kiểm tra sau này

## Job Data Management

### Input Format
```json
{
  "jobId": 123,
  "params": {
    // Job-specific parameters
  }
}
```

### Output Format
```json
{
  "jobId": 123,
  "success": true,
  "result": {
    // Job-specific results
  },
  "message": "Job completed successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Error Format
```json
{
  "success": false,
  "error": "Error message",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Monitoring & Debugging

### Logs
- `job-data/{jobId}/stdout.log`: Container stdout
- `job-data/{jobId}/stderr.log`: Container stderr
- Console logs từ job-listener.js

### Docker Commands
```bash
# Xem containers đang chạy
docker ps

# Xem logs container
docker logs datacoin-job-{jobId}

# Kill container (nếu cần)
docker kill datacoin-job-{jobId}

# Xem images
docker images | grep datacoin-
```

## Cleanup

### Automatic Cleanup
- Containers tự động xóa sau khi hoàn thành (--rm)
- Job data được giữ lại để audit
- Old job data (>30 ngày) tự động cleanup

### Manual Cleanup
```bash
# Xóa old job data
rm -rf job-data/{jobId}

# Xóa Docker images
docker rmi datacoin-data_processing:latest
docker rmi datacoin-ai_training:latest
```

## Troubleshooting

### Common Issues

1. **Docker not found**
   ```bash
   sudo apt-get update
   sudo apt-get install docker.io
   sudo usermod -aG docker $USER
   ```

2. **Permission denied**
   ```bash
   sudo chmod +x build-images.sh
   sudo chown -R $USER:$USER job-data/
   ```

3. **Container timeout**
   - Check container logs
   - Increase timeout in docker-utils.js
   - Optimize job processing logic

4. **Memory/CPU limits**
   - Adjust limits in docker-utils.js
   - Monitor resource usage

### Debug Mode
Set environment variable để enable debug:
```bash
DEBUG=1 node job-listener.js
```

## Result Verification (For Checkers)

### Verify Job Results
```bash
# Verify specific job
node checker-verify.js

# Or use as module
const JobChecker = require('./checker-verify');
const result = await JobChecker.verifyJobResult('jobId', 'expectedProviderAddress');
```

### Verification Process
1. **File Integrity**: Kiểm tra tất cả files cần thiết
2. **Hash Verification**: Tính lại hash và so sánh với result.hash
3. **Signature Verification**: Verify ECDSA signature với provider public key
4. **Report Generation**: Tạo verification report

### Verification Files
- `result.hash` - SHA256 hash của kết quả
- `signature.txt` - ECDSA signature và metadata
- `metadata.json` - Thông tin chi tiết về files
- `verification_report.json` - Kết quả verification
