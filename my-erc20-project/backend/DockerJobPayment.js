require('dotenv').config();
const { ethers } = require('ethers');
const Docker = require('dockerode');
const docker = new Docker();

const RPC_URL = process.env.RPC_URL;
const JOB_PAYMENT_ADDRESS = process.env.JOB_PAYMENT_ADDRESS;
const PRIVATE_KEY = process.env.BACKEND_PRIVATE_KEY;

const provider = new ethers.JsonRpcProvider(RPC_URL);
const wallet = new ethers.Wallet(PRIVATE_KEY, provider);

const abi = [
  "event JobRequested(uint256 indexed jobId, address indexed requester, string jobType, uint256 payment)",
  "function assignJob(uint256 jobId) external",
  "function startJob(uint256 jobId) external",
  "function partialPayment(uint256 jobId, uint256 amount) external",
  "function completeJob(uint256 jobId, string calldata resultHash) external",
  "function getJobDetails(uint256 jobId) external view returns (address requester, address provider, string jobType, uint256 cpu, uint256 mem, uint256 gpu, uint256 totalPayment, uint256 paidToProvider, uint8 status, string resultHash, uint256 createdAt, uint256 updatedAt)",
  "function jobTypes(string calldata jobType) external view returns (string image, bool exists)"
];

const contract = new ethers.Contract(JOB_PAYMENT_ADDRESS, abi, wallet);

async function runJob(jobId, jobType) {
  const jobInfo = await contract.getJobDetails(jobId);
  const totalPayment = jobInfo.totalPayment;
  const providerAddress = jobInfo.provider;

  const cpu = jobInfo.cpu || 1;       // mặc định 1 CPU nếu không có
  const mem = jobInfo.mem || 512;     // mặc định 512MB
  const gpu = jobInfo.gpu || 0;

  // Lấy docker image từ jobType
  const jt = await contract.jobTypes(jobType);
  if (!jt.exists) {
    console.error(`Job type ${jobType} không tồn tại`);
    return;
  }
  const image = jt.image;

  console.log(`🚀 Running job ${jobId} with image ${image} | CPU: ${cpu}, MEM: ${mem}MB, GPU: ${gpu}`);

  // Assign job trên chain
  const txAssign = await contract.assignJob(jobId);
  await txAssign.wait();

  // Start job trên chain
  const txStart = await contract.startJob(jobId);
  await txStart.wait();

  // Pull image
  await docker.pull(image, (err, stream) => {
    if (err) {
      console.error("Pull image lỗi:", err);
      throw err;
    }
    docker.modem.followProgress(stream, onFinished);

    function onFinished(err, output) {
      if (err) {
        console.error("Pull image thất bại:", err);
        throw err;
      }
    }
  });

  // Tạo container
  const container = await docker.createContainer({
    Image: image,
    Cmd: ["python", "main.py"],  // bạn tùy chỉnh cho đúng command job
    HostConfig: {
      AutoRemove: false,
      NanoCPUs: cpu * 1e9,
      Memory: mem * 1024 * 1024,
      DeviceRequests: gpu > 0 ? [{
        Count: gpu,
        Capabilities: [["gpu"]],
      }] : undefined,
    },
  });

  await container.start();

  // Hàm thanh toán từng phần (ví dụ 10 phần)
  let paid = ethers.BigNumber.from(0);
  const total = ethers.BigNumber.from(totalPayment);
  const step = total.div(10);

  let isJobDone = false;

  // Theo dõi log và tiến độ container
  const logStream = await container.logs({stdout: true, stderr: true, follow: true});
  logStream.on('data', chunk => {
    process.stdout.write(chunk.toString());

    // TODO: Có thể parse output để phát hiện job hoàn thành sớm
    if (chunk.toString().includes("JOB_DONE")) {
      isJobDone = true;
    }
  });

  // Thanh toán từng phần theo timer 10s
  while (!isJobDone && paid.lt(total)) {
    await new Promise(resolve => setTimeout(resolve, 10000));

    let payAmount = step;
    if (paid.add(step).gt(total)) {
      payAmount = total.sub(paid);
    }

    try {
      const txPartial = await contract.partialPayment(jobId, payAmount);
      await txPartial.wait();
      paid = paid.add(payAmount);
      console.log(`Partial payment done: ${paid.toString()}`);
    } catch (e) {
      console.error("Lỗi partialPayment:", e);
      break;
    }
  }

  // Nếu job chưa kết thúc thì dừng container
  if (!isJobDone) {
    console.log("Job chưa hoàn thành đúng hạn, dừng container.");
    await container.stop();
  } else {
    // Đọc kết quả hash (ví dụ từ file hoặc output - ở đây giả định)
    const resultHash = "result_hash_example";

    // Thanh toán phần còn lại nếu còn
    const remain = total.sub(paid);
    if (remain.gt(0)) {
      try {
        const txPartial = await contract.partialPayment(jobId, remain);
        await txPartial.wait();
        console.log(`Thanh toán phần còn lại: ${remain.toString()}`);
      } catch (e) {
        console.error("Lỗi thanh toán phần còn lại:", e);
      }
    }

    // Gọi completeJob
    try {
      const txComplete = await contract.completeJob(jobId, resultHash);
      await txComplete.wait();
      console.log(`✅ Job ${jobId} completed`);
    } catch (e) {
      console.error("Lỗi completeJob:", e);
    }

    await container.stop();
  }

  await container.remove();
}

contract.on("JobRequested", async (jobId, requester, jobType, payment) => {
  console.log(`New JobRequested event - jobId: ${jobId}, jobType: ${jobType}`);

  try {
    await runJob(jobId, jobType);
  } catch (e) {
    console.error("Error running job:", e);
  }
});

console.log("🚀 Backend listener started, waiting for JobRequested events...");
