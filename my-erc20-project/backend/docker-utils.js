const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const util = require('util');
const ResultAuthenticator = require('./result-authenticator');
const IPFSUploader = require('./ipfs-uploader');

const execAsync = util.promisify(exec);

class DockerManager {
  constructor(privateKey = null) {
    this.jobDataDir = path.join(__dirname, 'job-data');
    this.dockerDir = path.join(__dirname, 'docker');
    this.builtImages = new Set();

    // Initialize authenticator if private key provided
    if (privateKey) {
      this.authenticator = new ResultAuthenticator(privateKey);
      console.log(`🔐 Result authenticator initialized for provider: ${this.authenticator.providerAddress}`);
    }

    // Initialize IPFS uploader
    this.ipfsUploader = new IPFSUploader();
  }

  /**
   * Ensure job data directory exists
   */
  ensureJobDataDir(jobId) {
    const jobDir = path.join(this.jobDataDir, jobId.toString());
    if (!fs.existsSync(jobDir)) {
      fs.mkdirSync(jobDir, { recursive: true });
    }
    return jobDir;
  }

  /**
   * Build Docker image for a job type
   */
  async buildImage(jobType) {
    const imageName = `datacoin-${jobType}:latest`;

    // Check if image already exists
    try {
      const { stdout } = await execAsync(`sudo docker images -q ${imageName}`);
      if (stdout.trim()) {
        console.log(`📦 Image ${imageName} already exists`);
        this.builtImages.add(imageName);
        return imageName;
      }
    } catch (error) {
      // Image doesn't exist, continue to build
    }

    if (this.builtImages.has(imageName)) {
      console.log(`📦 Image ${imageName} already built`);
      return imageName;
    }

    const dockerfilePath = path.join(this.dockerDir, jobType);

    if (!fs.existsSync(dockerfilePath)) {
      throw new Error(`Dockerfile not found for job type: ${jobType}`);
    }

    console.log(`🔨 Building Docker image: ${imageName}`);

    try {
      const { stdout, stderr } = await execAsync(
        `sudo docker build -t ${imageName} ${dockerfilePath}`,
        { cwd: __dirname }
      );

      console.log(`✅ Successfully built image: ${imageName}`);
      this.builtImages.add(imageName);
      return imageName;

    } catch (error) {
      console.error(`❌ Failed to build image ${imageName}:`, error.message);
      throw error;
    }
  }

  /**
   * Create input.json for a job
   */
  createInputFile(jobId, jobData) {
    const jobDir = this.ensureJobDataDir(jobId);
    const inputPath = path.join(jobDir, 'input.json');
    
    fs.writeFileSync(inputPath, JSON.stringify(jobData, null, 2));
    console.log(`📝 Created input file: ${inputPath}`);
    
    return inputPath;
  }

  /**
   * Run a job in Docker container
   */
  async runJob(jobId, jobType, jobData) {
    try {
      // Build image if needed
      const imageName = await this.buildImage(jobType);
      
      // Prepare job directory
      const jobDir = this.ensureJobDataDir(jobId);
      const inputPath = this.createInputFile(jobId, jobData);
      
      // Docker run command with security restrictions
      const containerName = `datacoin-job-${jobId}`;
      const dockerCmd = [
        'sudo', 'docker', 'run',
        '--rm',                           // Auto-remove container
        '--read-only',                    // Read-only filesystem
        '--network=none',                 // No network access
        '--memory=512m',                  // Memory limit
        '--cpus=1',                       // CPU limit
        '--name', containerName,
        '-v', `${inputPath}:/app/input.json:ro`,  // Mount input (read-only)
        '-v', `${jobDir}:/output`,        // Mount output directory
        imageName
      ];

      console.log(`🚀 Running job ${jobId} (${jobType}) in container...`);
      console.log(`Command: ${dockerCmd.join(' ')}`);

      // Create log files
      const stdoutPath = path.join(jobDir, 'stdout.log');
      const stderrPath = path.join(jobDir, 'stderr.log');
      
      const stdoutStream = fs.createWriteStream(stdoutPath);
      const stderrStream = fs.createWriteStream(stderrPath);

      return new Promise((resolve, reject) => {
        const process = spawn(dockerCmd[0], dockerCmd.slice(1), {
          cwd: __dirname
        });

        // Capture stdout and stderr
        process.stdout.pipe(stdoutStream);
        process.stderr.pipe(stderrStream);

        // Also log to console
        process.stdout.on('data', (data) => {
          console.log(`[${jobId}] ${data.toString().trim()}`);
        });

        process.stderr.on('data', (data) => {
          console.error(`[${jobId}] ERROR: ${data.toString().trim()}`);
        });

        process.on('close', async (code) => {
          stdoutStream.end();
          stderrStream.end();

          if (code === 0) {
            console.log(`✅ Job ${jobId} completed successfully`);
            try {
              const results = await this.collectResults(jobId);
              resolve(results);
            } catch (error) {
              console.error(`❌ Error collecting results for job ${jobId}:`, error);
              reject(error);
            }
          } else {
            console.error(`❌ Job ${jobId} failed with exit code: ${code}`);
            reject(new Error(`Container exited with code ${code}`));
          }
        });

        process.on('error', (error) => {
          stdoutStream.end();
          stderrStream.end();
          console.error(`❌ Failed to start container for job ${jobId}:`, error);
          reject(error);
        });

        // Set timeout (5 minutes)
        setTimeout(() => {
          console.log(`⏰ Job ${jobId} timeout, killing container...`);
          this.killContainer(containerName);
          reject(new Error('Job timeout'));
        }, 5 * 60 * 1000);
      });

    } catch (error) {
      console.error(`❌ Error running job ${jobId}:`, error);
      throw error;
    }
  }

  /**
   * Kill a running container
   */
  async killContainer(containerName) {
    try {
      await execAsync(`sudo docker kill ${containerName}`);
      console.log(`🔪 Killed container: ${containerName}`);
    } catch (error) {
      console.log(`Container ${containerName} not found or already stopped`);
    }
  }

  /**
   * Collect results from job directory
   */
  async collectResults(jobId) {
    const jobDir = path.join(this.jobDataDir, jobId.toString());
    const outputPath = path.join(jobDir, 'output.json');
    const stdoutPath = path.join(jobDir, 'stdout.log');
    const stderrPath = path.join(jobDir, 'stderr.log');

    let output = null;
    let stdout = '';
    let stderr = '';

    // Read output.json
    if (fs.existsSync(outputPath)) {
      try {
        output = JSON.parse(fs.readFileSync(outputPath, 'utf8'));
      } catch (error) {
        console.error(`❌ Failed to parse output.json for job ${jobId}:`, error);
      }
    }

    // Read logs
    if (fs.existsSync(stdoutPath)) {
      stdout = fs.readFileSync(stdoutPath, 'utf8');
    }

    if (fs.existsSync(stderrPath)) {
      stderr = fs.readFileSync(stderrPath, 'utf8');
    }

    // Authenticate results if authenticator is available
    let authentication = null;
    if (this.authenticator) {
      try {
        authentication = await this.authenticator.authenticateJobResult(jobId, jobDir);
        console.log(`🔐 Job ${jobId} results authenticated successfully`);
      } catch (error) {
        console.error(`❌ Failed to authenticate job ${jobId} results:`, error);
        // Don't fail the job, just log the error
      }
    }

    // Upload to IPFS if uploader is available
    let ipfsUpload = null;
    if (this.ipfsUploader) {
      try {
        ipfsUpload = await this.ipfsUploader.uploadJobArtifact(jobId, jobDir);
        console.log(`📤 Job ${jobId} artifacts uploaded to IPFS: ${ipfsUpload.cid}`);
      } catch (error) {
        console.error(`❌ Failed to upload job ${jobId} to IPFS:`, error);
        // Don't fail the job, just log the error
      }
    }

    return {
      output,
      stdout,
      stderr,
      jobDir,
      authentication,
      ipfsUpload
    };
  }

  /**
   * Cleanup old job data (keep for audit)
   */
  async cleanupOldJobs(daysToKeep = 30) {
    const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
    
    if (!fs.existsSync(this.jobDataDir)) {
      return;
    }

    const jobDirs = fs.readdirSync(this.jobDataDir);
    
    for (const jobDir of jobDirs) {
      const jobPath = path.join(this.jobDataDir, jobDir);
      const stats = fs.statSync(jobPath);
      
      if (stats.isDirectory() && stats.mtime.getTime() < cutoffTime) {
        console.log(`🗑️ Cleaning up old job data: ${jobDir}`);
        fs.rmSync(jobPath, { recursive: true, force: true });
      }
    }
  }
}

module.exports = DockerManager;
