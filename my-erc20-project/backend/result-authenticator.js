const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const { ethers } = require('ethers');

class ResultAuthenticator {
  constructor(privateKey) {
    this.wallet = new ethers.Wallet(privateKey);
    this.providerAddress = this.wallet.address;
  }

  /**
   * Bước 1.2: Tạo hash của kết quả
   * Gom tất cả file cần chứng thực và tính SHA256 hash
   */
  async createResultHash(jobId, jobDataDir) {
    console.log(`🔐 Creating result hash for job ${jobId}...`);
    
    const filesToHash = [
      'output.json',
      'stdout.log', 
      'stderr.log'
    ];

    let combinedData = '';
    const metadata = {
      jobId: jobId,
      timestamp: new Date().toISOString(),
      provider: this.providerAddress,
      files: []
    };

    // Đọ<PERSON> và nối dữ liệu từ tất cả files
    for (const fileName of filesToHash) {
      const filePath = path.join(jobDataDir, fileName);
      
      if (fs.existsSync(filePath)) {
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const fileSize = fs.statSync(filePath).size;
        
        // Thêm metadata của file
        metadata.files.push({
          name: fileName,
          size: fileSize,
          hash: crypto.createHash('sha256').update(fileContent).digest('hex')
        });
        
        // Nối dữ liệu với separator
        combinedData += `--- FILE: ${fileName} ---\n`;
        combinedData += fileContent;
        combinedData += `\n--- END: ${fileName} ---\n`;
        
        console.log(`📄 Added ${fileName} (${fileSize} bytes)`);
      } else {
        console.log(`⚠️ File ${fileName} not found, skipping...`);
      }
    }

    // Thêm metadata vào cuối
    const metadataJson = JSON.stringify(metadata, null, 2);
    combinedData += `--- METADATA ---\n${metadataJson}\n--- END METADATA ---\n`;

    // Tính SHA256 hash
    const resultHash = crypto.createHash('sha256').update(combinedData).digest('hex');
    
    // Lưu hash vào file
    const hashFilePath = path.join(jobDataDir, 'result.hash');
    fs.writeFileSync(hashFilePath, resultHash);
    
    // Lưu metadata
    const metadataFilePath = path.join(jobDataDir, 'metadata.json');
    fs.writeFileSync(metadataFilePath, metadataJson);
    
    // Lưu combined data để debug (optional)
    const combinedDataPath = path.join(jobDataDir, 'combined_data.txt');
    fs.writeFileSync(combinedDataPath, combinedData);

    console.log(`✅ Result hash created: ${resultHash}`);
    console.log(`📁 Hash saved to: ${hashFilePath}`);
    
    return {
      hash: resultHash,
      metadata: metadata,
      combinedDataSize: combinedData.length
    };
  }

  /**
   * Bước 1.3: Ký hash bằng private key của provider
   * Dùng ECDSA để ký result.hash
   */
  async signResultHash(jobId, jobDataDir, resultHash) {
    console.log(`✍️ Signing result hash for job ${jobId}...`);
    
    try {
      // Tạo message để ký (thêm prefix để tương thích với Ethereum)
      const message = `DATACOIN Job Result\nJob ID: ${jobId}\nHash: ${resultHash}\nProvider: ${this.providerAddress}`;
      
      // Ký message bằng private key
      const signature = await this.wallet.signMessage(message);
      
      // Tạo signature data với thông tin chi tiết
      const signatureData = {
        jobId: jobId,
        resultHash: resultHash,
        provider: this.providerAddress,
        message: message,
        signature: signature,
        timestamp: new Date().toISOString(),
        // Thêm thông tin để verify
        verification: {
          messageHash: ethers.hashMessage(message),
          recoveredAddress: ethers.verifyMessage(message, signature)
        }
      };

      // Lưu chữ ký vào file
      const signatureFilePath = path.join(jobDataDir, 'signature.txt');
      fs.writeFileSync(signatureFilePath, JSON.stringify(signatureData, null, 2));
      
      console.log(`✅ Signature created and saved to: ${signatureFilePath}`);
      console.log(`🔑 Provider address: ${this.providerAddress}`);
      console.log(`📝 Signature: ${signature}`);
      
      return signatureData;
      
    } catch (error) {
      console.error(`❌ Error signing result hash:`, error);
      throw error;
    }
  }

  /**
   * Xử lý hoàn chỉnh: hash + ký
   */
  async authenticateJobResult(jobId, jobDataDir) {
    console.log(`🔒 Authenticating job result for job ${jobId}...`);
    
    try {
      // Bước 1.2: Tạo hash
      const hashResult = await this.createResultHash(jobId, jobDataDir);
      
      // Bước 1.3: Ký hash
      const signatureResult = await this.signResultHash(jobId, jobDataDir, hashResult.hash);
      
      console.log(`🎉 Job result authentication completed for job ${jobId}`);
      
      return {
        hash: hashResult,
        signature: signatureResult,
        files: {
          hash: path.join(jobDataDir, 'result.hash'),
          signature: path.join(jobDataDir, 'signature.txt'),
          metadata: path.join(jobDataDir, 'metadata.json')
        }
      };
      
    } catch (error) {
      console.error(`❌ Error authenticating job result:`, error);
      throw error;
    }
  }

  /**
   * Verify signature (cho Checker sử dụng sau này)
   */
  static async verifyJobResult(jobDataDir, expectedProviderAddress = null) {
    console.log(`🔍 Verifying job result...`);
    
    try {
      // Đọc signature file
      const signatureFilePath = path.join(jobDataDir, 'signature.txt');
      if (!fs.existsSync(signatureFilePath)) {
        throw new Error('Signature file not found');
      }
      
      const signatureData = JSON.parse(fs.readFileSync(signatureFilePath, 'utf8'));
      
      // Verify signature
      const recoveredAddress = ethers.verifyMessage(signatureData.message, signatureData.signature);
      
      const isValid = recoveredAddress.toLowerCase() === signatureData.provider.toLowerCase();
      
      if (expectedProviderAddress) {
        const isExpectedProvider = recoveredAddress.toLowerCase() === expectedProviderAddress.toLowerCase();
        if (!isExpectedProvider) {
          throw new Error(`Provider mismatch. Expected: ${expectedProviderAddress}, Got: ${recoveredAddress}`);
        }
      }
      
      console.log(`✅ Signature verification: ${isValid ? 'VALID' : 'INVALID'}`);
      console.log(`🔑 Recovered address: ${recoveredAddress}`);
      
      return {
        isValid: isValid,
        recoveredAddress: recoveredAddress,
        signatureData: signatureData
      };
      
    } catch (error) {
      console.error(`❌ Error verifying job result:`, error);
      throw error;
    }
  }
}

module.exports = ResultAuthenticator;
