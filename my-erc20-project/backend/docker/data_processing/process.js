const fs = require('fs');
const path = require('path');

async function processDataJob() {
  try {
    console.log('Starting data processing job...');
    
    // Read input.json
    const inputPath = '/app/input.json';
    if (!fs.existsSync(inputPath)) {
      throw new Error('input.json not found');
    }
    
    const inputData = JSON.parse(fs.readFileSync(inputPath, 'utf8'));
    console.log('Input data:', inputData);
    
    // Extract parameters
    const { jobId, params } = inputData;
    let { numbers } = params;

    // Handle both string and array formats
    if (typeof numbers === 'string') {
      // Convert comma-separated string to array of numbers
      numbers = numbers.split(',').map(str => {
        const num = parseFloat(str.trim());
        if (isNaN(num)) {
          throw new Error(`Invalid number: ${str.trim()}`);
        }
        return num;
      });
    }

    if (!Array.isArray(numbers)) {
      throw new Error("Missing 'numbers' array in params");
    }

    if (numbers.length === 0) {
      throw new Error("Numbers array cannot be empty");
    }
    
    // Process data - calculate sum
    const sum = numbers.reduce((a, b) => a + b, 0);
    const average = sum / numbers.length;
    const max = Math.max(...numbers);
    const min = Math.min(...numbers);
    
    // Prepare result
    const result = {
      jobId,
      success: true,
      result: {
        sum,
        average,
        max,
        min,
        count: numbers.length
      },
      message: `Data processing completed successfully. Sum: ${sum}`,
      timestamp: new Date().toISOString()
    };
    
    // Write output.json
    const outputPath = '/output/output.json';
    fs.writeFileSync(outputPath, JSON.stringify(result, null, 2));
    
    console.log('Data processing completed successfully');
    console.log('Result:', result);
    
  } catch (error) {
    console.error('Error in data processing:', error.message);
    
    // Write error output
    const errorResult = {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
    
    try {
      fs.writeFileSync('/output/output.json', JSON.stringify(errorResult, null, 2));
    } catch (writeError) {
      console.error('Failed to write error output:', writeError.message);
    }
    
    process.exit(1);
  }
}

// Run the job
processDataJob();
