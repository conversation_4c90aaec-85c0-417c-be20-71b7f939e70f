FROM python:3.11-slim

# Create app directory
WORKDIR /app

# Install system dependencies for building numpy/scikit-learn
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt ./

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY process.py ./

# Create output directory
RUN mkdir -p /output

# Create non-root user
RUN useradd -m -s /bin/bash appuser
USER appuser

# Run the application
CMD ["python", "process.py"]
