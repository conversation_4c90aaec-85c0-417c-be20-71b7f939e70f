#!/usr/bin/env python3

import json
import os
import sys
import time
import numpy as np
from datetime import datetime
from sklearn.linear_model import LinearRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score

def process_ai_training():
    try:
        print("Starting AI training job...")
        
        # Read input.json
        input_path = '/app/input.json'
        if not os.path.exists(input_path):
            raise Exception('input.json not found')
        
        with open(input_path, 'r') as f:
            input_data = json.load(f)
        
        print(f"Input data: {input_data}")
        
        # Extract parameters
        job_id = input_data.get('jobId')
        params = input_data.get('params', {})
        
        # Get training parameters
        dataset_size = params.get('dataset_size', 1000)
        epochs = params.get('epochs', 10)
        model_type = params.get('model_type', 'linear_regression')
        
        print(f"Training parameters: dataset_size={dataset_size}, epochs={epochs}, model_type={model_type}")
        
        # Simulate AI training process
        if model_type == 'linear_regression':
            result = train_linear_regression(dataset_size, epochs)
        else:
            result = simulate_generic_training(dataset_size, epochs)
        
        # Prepare output
        output = {
            'jobId': job_id,
            'success': True,
            'result': result,
            'message': f'AI training completed successfully. Model: {model_type}',
            'timestamp': datetime.now().isoformat()
        }
        
        # Write output.json
        output_path = '/output/output.json'
        with open(output_path, 'w') as f:
            json.dump(output, f, indent=2)
        
        print("AI training completed successfully")
        print(f"Result: {result}")
        
    except Exception as error:
        print(f"Error in AI training: {str(error)}")
        
        # Write error output
        error_result = {
            'success': False,
            'error': str(error),
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            with open('/output/output.json', 'w') as f:
                json.dump(error_result, f, indent=2)
        except Exception as write_error:
            print(f"Failed to write error output: {str(write_error)}")
        
        sys.exit(1)

def train_linear_regression(dataset_size, epochs):
    """Simulate linear regression training"""
    print("Training linear regression model...")
    
    # Generate synthetic dataset
    np.random.seed(42)
    X = np.random.randn(dataset_size, 3)
    y = 2 * X[:, 0] + 3 * X[:, 1] - X[:, 2] + np.random.randn(dataset_size) * 0.1
    
    # Split dataset
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Train model
    model = LinearRegression()
    
    # Simulate training epochs
    for epoch in range(epochs):
        model.fit(X_train, y_train)
        if epoch % 2 == 0:
            print(f"Epoch {epoch + 1}/{epochs} completed")
        time.sleep(0.1)  # Simulate training time
    
    # Evaluate model
    y_pred = model.predict(X_test)
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    return {
        'model_type': 'linear_regression',
        'dataset_size': dataset_size,
        'epochs': epochs,
        'mse': float(mse),
        'r2_score': float(r2),
        'coefficients': model.coef_.tolist(),
        'intercept': float(model.intercept_),
        'training_samples': len(X_train),
        'test_samples': len(X_test)
    }

def simulate_generic_training(dataset_size, epochs):
    """Simulate generic AI training"""
    print(f"Simulating generic AI training...")
    
    # Simulate training progress
    for epoch in range(epochs):
        # Simulate some computation
        time.sleep(0.1)
        loss = 1.0 / (epoch + 1)  # Decreasing loss
        accuracy = min(0.95, 0.5 + (epoch * 0.05))  # Increasing accuracy
        
        if epoch % 2 == 0:
            print(f"Epoch {epoch + 1}/{epochs} - Loss: {loss:.4f}, Accuracy: {accuracy:.4f}")
    
    return {
        'model_type': 'generic',
        'dataset_size': dataset_size,
        'epochs': epochs,
        'final_loss': loss,
        'final_accuracy': accuracy,
        'training_time_seconds': epochs * 0.1
    }

if __name__ == "__main__":
    process_ai_training()
