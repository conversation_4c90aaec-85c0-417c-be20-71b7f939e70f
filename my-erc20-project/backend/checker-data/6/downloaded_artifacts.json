{"jobId": 6, "timestamp": "2025-08-12T06:48:38.444Z", "files": {"output.json": {"content": "{\n  \"jobId\": 6,\n  \"success\": true,\n  \"result\": {\n    \"sum\": 6,\n    \"average\": 3,\n    \"max\": 4,\n    \"min\": 2,\n    \"count\": 2\n  },\n  \"message\": \"Data processing completed successfully. Sum: 6\",\n  \"timestamp\": \"2025-08-12T06:48:38.283Z\"\n}", "size": 235, "encoding": "utf8"}, "stdout.log": {"content": "Starting data processing job...\nInput data: { jobId: 6, params: { numbers: '2,4' } }\nData processing completed successfully\nResult: {\n  jobId: 6,\n  success: true,\n  result: { sum: 6, average: 3, max: 4, min: 2, count: 2 },\n  message: 'Data processing completed successfully. Sum: 6',\n  timestamp: '2025-08-12T06:48:38.283Z'\n}\n", "size": 326, "encoding": "utf8"}, "stderr.log": {"content": "", "size": 0, "encoding": "utf8"}, "result.hash": {"content": "298a2be5a60b76894f7a1a88332275a17301f754c1b56279c597485ad4c39332", "size": 64, "encoding": "utf8"}, "signature.txt": {"content": "{\n  \"jobId\": 6,\n  \"resultHash\": \"298a2be5a60b76894f7a1a88332275a17301f754c1b56279c597485ad4c39332\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"message\": \"DATACOIN Job Result\\nJob ID: 6\\nHash: 298a2be5a60b76894f7a1a88332275a17301f754c1b56279c597485ad4c39332\\nProvider: 0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"signature\": \"0xeef61a76d695c74e2736edef59eb24488e47332656376626fad2a00908d7bfd93cee02691105c8265df0fd14d0b54f4f7e99234564b3ff5e22c5ee6170de7f221c\",\n  \"timestamp\": \"2025-08-12T06:48:38.432Z\",\n  \"verification\": {\n    \"messageHash\": \"0x5928499817c4c8cd16243bea26e54180fe9eea3e9523d888864f03157febd48b\",\n    \"recoveredAddress\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\"\n  }\n}", "size": 710, "encoding": "utf8"}, "metadata.json": {"content": "{\n  \"jobId\": 6,\n  \"timestamp\": \"2025-08-12T06:48:38.416Z\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"files\": [\n    {\n      \"name\": \"output.json\",\n      \"size\": 235,\n      \"hash\": \"11c3157e903f16f2a9cbf509ab245bd083fb655b9769427390f00bac627559d6\"\n    },\n    {\n      \"name\": \"stdout.log\",\n      \"size\": 326,\n      \"hash\": \"fb907a5f3fa57f7983ba53863db218f6e5b9bb4f44ca292d82e8255922d32046\"\n    },\n    {\n      \"name\": \"stderr.log\",\n      \"size\": 0,\n      \"hash\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\"\n    }\n  ]\n}", "size": 558, "encoding": "utf8"}}, "manifest": {"version": "1.0", "jobId": 6, "createdAt": "2025-08-12T06:48:38.444Z", "fileCount": 6, "totalSize": 1893}}