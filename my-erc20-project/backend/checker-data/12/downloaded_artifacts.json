{"jobId": 12, "timestamp": "2025-08-12T07:37:04.238Z", "files": {"output.json": {"content": "{\n  \"jobId\": 12,\n  \"success\": true,\n  \"result\": {\n    \"sum\": 13,\n    \"average\": 6.5,\n    \"max\": 7,\n    \"min\": 6,\n    \"count\": 2\n  },\n  \"message\": \"Data processing completed successfully. Sum: 13\",\n  \"timestamp\": \"2025-08-12T07:37:04.125Z\"\n}", "size": 240, "encoding": "utf8"}, "stdout.log": {"content": "Starting data processing job...\nInput data: { jobId: 12, params: { numbers: '6,7' } }\nData processing completed successfully\nResult: {\n  jobId: 12,\n  success: true,\n  result: { sum: 13, average: 6.5, max: 7, min: 6, count: 2 },\n  message: 'Data processing completed successfully. Sum: 13',\n  timestamp: '2025-08-12T07:37:04.125Z'\n}\n", "size": 332, "encoding": "utf8"}, "stderr.log": {"content": "", "size": 0, "encoding": "utf8"}, "result.hash": {"content": "8184425ec627218b140ccd29ee97a524749727c397fbfd96b15293ae54bcfdf6", "size": 64, "encoding": "utf8"}, "signature.txt": {"content": "{\n  \"jobId\": 12,\n  \"resultHash\": \"8184425ec627218b140ccd29ee97a524749727c397fbfd96b15293ae54bcfdf6\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"message\": \"DATACOIN Job Result\\nJob ID: 12\\nHash: 8184425ec627218b140ccd29ee97a524749727c397fbfd96b15293ae54bcfdf6\\nProvider: 0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"signature\": \"0x3c34a063cffb4e72a8d0d1a40a0b84a72e8ec469ed6196f2ae9c9367824b6e7a5e8892eda3f66366365794d5a794eaa8c6204af96f35b94a22368954c63e63131c\",\n  \"timestamp\": \"2025-08-12T07:37:04.229Z\",\n  \"verification\": {\n    \"messageHash\": \"0x7ec48b6a9470bd719ea933f164b0e7beecd55781e08a09ff415ec240aa0db6c5\",\n    \"recoveredAddress\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\"\n  }\n}", "size": 712, "encoding": "utf8"}, "metadata.json": {"content": "{\n  \"jobId\": 12,\n  \"timestamp\": \"2025-08-12T07:37:04.221Z\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"files\": [\n    {\n      \"name\": \"output.json\",\n      \"size\": 240,\n      \"hash\": \"95bd48c62cde28b1e76d76a485c081ece9553c3144265d118da4ecbbd4c6c387\"\n    },\n    {\n      \"name\": \"stdout.log\",\n      \"size\": 332,\n      \"hash\": \"418350ae836985a60c78a3690769d80ae697b5eb66f96ba64ff7e2a885c920ba\"\n    },\n    {\n      \"name\": \"stderr.log\",\n      \"size\": 0,\n      \"hash\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\"\n    }\n  ]\n}", "size": 559, "encoding": "utf8"}}, "manifest": {"version": "1.0", "jobId": 12, "createdAt": "2025-08-12T07:37:04.238Z", "fileCount": 6, "totalSize": 1907}}