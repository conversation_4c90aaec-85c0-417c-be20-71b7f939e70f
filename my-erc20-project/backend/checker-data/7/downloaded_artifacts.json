{"jobId": 7, "timestamp": "2025-08-12T07:14:54.458Z", "files": {"output.json": {"content": "{\n  \"jobId\": 7,\n  \"success\": true,\n  \"result\": {\n    \"sum\": 14,\n    \"average\": 7,\n    \"max\": 8,\n    \"min\": 6,\n    \"count\": 2\n  },\n  \"message\": \"Data processing completed successfully. Sum: 14\",\n  \"timestamp\": \"2025-08-12T07:14:54.347Z\"\n}", "size": 237, "encoding": "utf8"}, "stdout.log": {"content": "Starting data processing job...\nInput data: { jobId: 7, params: { numbers: '6,8' } }\nData processing completed successfully\nResult: {\n  jobId: 7,\n  success: true,\n  result: { sum: 14, average: 7, max: 8, min: 6, count: 2 },\n  message: 'Data processing completed successfully. Sum: 14',\n  timestamp: '2025-08-12T07:14:54.347Z'\n}\n", "size": 328, "encoding": "utf8"}, "stderr.log": {"content": "", "size": 0, "encoding": "utf8"}, "result.hash": {"content": "5c8292a4e3e6ce64f328e260622da6709a628eab7fe25f3b95c007290df0e2ad", "size": 64, "encoding": "utf8"}, "signature.txt": {"content": "{\n  \"jobId\": 7,\n  \"resultHash\": \"5c8292a4e3e6ce64f328e260622da6709a628eab7fe25f3b95c007290df0e2ad\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"message\": \"DATACOIN Job Result\\nJob ID: 7\\nHash: 5c8292a4e3e6ce64f328e260622da6709a628eab7fe25f3b95c007290df0e2ad\\nProvider: 0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"signature\": \"0xa586e9a3afbf3ad935e91abc29536c3ff11a15be05d3668539c3f637ecc49b6c503b73d2695658cb50965b83e453adf0983f6662af4e1336bffb3b084bac979a1b\",\n  \"timestamp\": \"2025-08-12T07:14:54.448Z\",\n  \"verification\": {\n    \"messageHash\": \"0x3a16bc5fc6336599d5f3ef73aa6fd419e195bd362c341ccd9384e78816c5c2bd\",\n    \"recoveredAddress\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\"\n  }\n}", "size": 710, "encoding": "utf8"}, "metadata.json": {"content": "{\n  \"jobId\": 7,\n  \"timestamp\": \"2025-08-12T07:14:54.442Z\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"files\": [\n    {\n      \"name\": \"output.json\",\n      \"size\": 237,\n      \"hash\": \"b485e81240f82823d41cbc89d893192adeee754f456d25dd3fa8f0eb1801e651\"\n    },\n    {\n      \"name\": \"stdout.log\",\n      \"size\": 328,\n      \"hash\": \"5b19f89c856546661eb4f4dc1b0569836976159edcab04115225349268f45afc\"\n    },\n    {\n      \"name\": \"stderr.log\",\n      \"size\": 0,\n      \"hash\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\"\n    }\n  ]\n}", "size": 558, "encoding": "utf8"}}, "manifest": {"version": "1.0", "jobId": 7, "createdAt": "2025-08-12T07:14:54.458Z", "fileCount": 6, "totalSize": 1897}}