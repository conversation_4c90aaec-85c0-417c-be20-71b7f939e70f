{"jobId": 9, "timestamp": "2025-08-12T07:22:19.550Z", "files": {"output.json": {"content": "{\n  \"jobId\": 9,\n  \"success\": true,\n  \"result\": {\n    \"sum\": 25,\n    \"average\": 12.5,\n    \"max\": 20,\n    \"min\": 5,\n    \"count\": 2\n  },\n  \"message\": \"Data processing completed successfully. Sum: 25\",\n  \"timestamp\": \"2025-08-12T07:22:19.387Z\"\n}", "size": 241, "encoding": "utf8"}, "stdout.log": {"content": "Starting data processing job...\nInput data: { jobId: 9, params: { numbers: '20,5' } }\nData processing completed successfully\nResult: {\n  jobId: 9,\n  success: true,\n  result: { sum: 25, average: 12.5, max: 20, min: 5, count: 2 },\n  message: 'Data processing completed successfully. Sum: 25',\n  timestamp: '2025-08-12T07:22:19.387Z'\n}\n", "size": 333, "encoding": "utf8"}, "stderr.log": {"content": "", "size": 0, "encoding": "utf8"}, "result.hash": {"content": "f2564666e818125d7ccfcff1c8022d4b32e7e6183c1c9366417e152713fe83b1", "size": 64, "encoding": "utf8"}, "signature.txt": {"content": "{\n  \"jobId\": 9,\n  \"resultHash\": \"f2564666e818125d7ccfcff1c8022d4b32e7e6183c1c9366417e152713fe83b1\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"message\": \"DATACOIN Job Result\\nJob ID: 9\\nHash: f2564666e818125d7ccfcff1c8022d4b32e7e6183c1c9366417e152713fe83b1\\nProvider: 0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"signature\": \"0x63f910e03f28b0d703bfaf142571cf634ee38a51882e4f853de5b9696a690e4b22f974404bbb8d8a1203ca7e17c0882e2b44432756c183b579274c557e2ce2101b\",\n  \"timestamp\": \"2025-08-12T07:22:19.538Z\",\n  \"verification\": {\n    \"messageHash\": \"0xe4e838d61c4142fce4c3782b8a0567aeb6a264a10b15d3d569d990f69a0c36f0\",\n    \"recoveredAddress\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\"\n  }\n}", "size": 710, "encoding": "utf8"}, "metadata.json": {"content": "{\n  \"jobId\": 9,\n  \"timestamp\": \"2025-08-12T07:22:19.522Z\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"files\": [\n    {\n      \"name\": \"output.json\",\n      \"size\": 241,\n      \"hash\": \"83dffa203175ba79c777711a4f3f2b4fdf5b5e2311ce8000359341e10a78acd0\"\n    },\n    {\n      \"name\": \"stdout.log\",\n      \"size\": 333,\n      \"hash\": \"413b0b6e20a401d38a493e3e4ad1da51a1e0d06ea2313514aa4858d03c5eebda\"\n    },\n    {\n      \"name\": \"stderr.log\",\n      \"size\": 0,\n      \"hash\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\"\n    }\n  ]\n}", "size": 558, "encoding": "utf8"}}, "manifest": {"version": "1.0", "jobId": 9, "createdAt": "2025-08-12T07:22:19.550Z", "fileCount": 6, "totalSize": 1906}}