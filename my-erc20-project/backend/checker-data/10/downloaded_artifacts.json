{"jobId": 10, "timestamp": "2025-08-12T07:27:05.649Z", "files": {"output.json": {"content": "{\n  \"jobId\": 10,\n  \"success\": true,\n  \"result\": {\n    \"sum\": 14,\n    \"average\": 7,\n    \"max\": 8,\n    \"min\": 6,\n    \"count\": 2\n  },\n  \"message\": \"Data processing completed successfully. Sum: 14\",\n  \"timestamp\": \"2025-08-12T07:27:05.559Z\"\n}", "size": 238, "encoding": "utf8"}, "stdout.log": {"content": "Starting data processing job...\nInput data: { jobId: 10, params: { numbers: '6,8' } }\nData processing completed successfully\nResult: {\n  jobId: 10,\n  success: true,\n  result: { sum: 14, average: 7, max: 8, min: 6, count: 2 },\n  message: 'Data processing completed successfully. Sum: 14',\n  timestamp: '2025-08-12T07:27:05.559Z'\n}\n", "size": 330, "encoding": "utf8"}, "stderr.log": {"content": "", "size": 0, "encoding": "utf8"}, "result.hash": {"content": "cc1e860a94b765a6ac959e205904a79482b0a339585c1f5f16d9bb781bdce3c7", "size": 64, "encoding": "utf8"}, "signature.txt": {"content": "{\n  \"jobId\": 10,\n  \"resultHash\": \"cc1e860a94b765a6ac959e205904a79482b0a339585c1f5f16d9bb781bdce3c7\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"message\": \"DATACOIN Job Result\\nJob ID: 10\\nHash: cc1e860a94b765a6ac959e205904a79482b0a339585c1f5f16d9bb781bdce3c7\\nProvider: 0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"signature\": \"0x7f307705e837a7bde5ce12f4691bafc484456db62edac97d20c8f6a382af01a10b4737bb9e84fabd6967c6270645378c71ddd038b3297720e19ad001879b216f1c\",\n  \"timestamp\": \"2025-08-12T07:27:05.639Z\",\n  \"verification\": {\n    \"messageHash\": \"0x7776dcd7dbe2284ac1c10b3e0ae1a891954c839fafc24e8cc8695fb9d4790c74\",\n    \"recoveredAddress\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\"\n  }\n}", "size": 712, "encoding": "utf8"}, "metadata.json": {"content": "{\n  \"jobId\": 10,\n  \"timestamp\": \"2025-08-12T07:27:05.635Z\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"files\": [\n    {\n      \"name\": \"output.json\",\n      \"size\": 238,\n      \"hash\": \"1c0a05ccf8943cba23a716e9c96bd10cd72eac9ca9490b564b399c8a6b7bcaa7\"\n    },\n    {\n      \"name\": \"stdout.log\",\n      \"size\": 330,\n      \"hash\": \"1790b67b456bc5af2c1c9d3fb09c1ef32cf96cc52e588bda7854fafff5586199\"\n    },\n    {\n      \"name\": \"stderr.log\",\n      \"size\": 0,\n      \"hash\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\"\n    }\n  ]\n}", "size": 559, "encoding": "utf8"}}, "manifest": {"version": "1.0", "jobId": 10, "createdAt": "2025-08-12T07:27:05.649Z", "fileCount": 6, "totalSize": 1903}}