{"jobId": 11, "timestamp": "2025-08-12T07:36:23.855Z", "files": {"output.json": {"content": "{\n  \"jobId\": 11,\n  \"success\": true,\n  \"result\": {\n    \"sum\": 6,\n    \"average\": 3,\n    \"max\": 4,\n    \"min\": 2,\n    \"count\": 2\n  },\n  \"message\": \"Data processing completed successfully. Sum: 6\",\n  \"timestamp\": \"2025-08-12T07:36:23.761Z\"\n}", "size": 236, "encoding": "utf8"}, "stdout.log": {"content": "Starting data processing job...\nInput data: { jobId: 11, params: { numbers: '2,4' } }\nData processing completed successfully\nResult: {\n  jobId: 11,\n  success: true,\n  result: { sum: 6, average: 3, max: 4, min: 2, count: 2 },\n  message: 'Data processing completed successfully. Sum: 6',\n  timestamp: '2025-08-12T07:36:23.761Z'\n}\n", "size": 328, "encoding": "utf8"}, "stderr.log": {"content": "", "size": 0, "encoding": "utf8"}, "result.hash": {"content": "db01a0f01bbdabfa997eaa99b672d266c11d431341830619375fc61899e963f9", "size": 64, "encoding": "utf8"}, "signature.txt": {"content": "{\n  \"jobId\": 11,\n  \"resultHash\": \"db01a0f01bbdabfa997eaa99b672d266c11d431341830619375fc61899e963f9\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"message\": \"DATACOIN Job Result\\nJob ID: 11\\nHash: db01a0f01bbdabfa997eaa99b672d266c11d431341830619375fc61899e963f9\\nProvider: 0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"signature\": \"0xfb81dc16d20044b07387484c1f31dc05a30b8a945935f8b6430db1329af340a766fea7b792e7031c41fddeb766cc2f29a493e0d3ff449998f6ecef89684ae03d1c\",\n  \"timestamp\": \"2025-08-12T07:36:23.844Z\",\n  \"verification\": {\n    \"messageHash\": \"0x6af209c932dbe68397af89cbd91210f83f669ef64447190aaf25a71d27af0dfc\",\n    \"recoveredAddress\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\"\n  }\n}", "size": 712, "encoding": "utf8"}, "metadata.json": {"content": "{\n  \"jobId\": 11,\n  \"timestamp\": \"2025-08-12T07:36:23.837Z\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"files\": [\n    {\n      \"name\": \"output.json\",\n      \"size\": 236,\n      \"hash\": \"046edc3859df7b45784abba30bc5595eeadbff9e273b2acc2682aa5152a63d03\"\n    },\n    {\n      \"name\": \"stdout.log\",\n      \"size\": 328,\n      \"hash\": \"4803a4076a4419d8c3853b1311f086e2102de6cea9c9363b892d04165c7c3082\"\n    },\n    {\n      \"name\": \"stderr.log\",\n      \"size\": 0,\n      \"hash\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\"\n    }\n  ]\n}", "size": 559, "encoding": "utf8"}}, "manifest": {"version": "1.0", "jobId": 11, "createdAt": "2025-08-12T07:36:23.855Z", "fileCount": 6, "totalSize": 1899}}