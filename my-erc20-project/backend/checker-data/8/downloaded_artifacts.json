{"jobId": 8, "timestamp": "2025-08-12T07:18:49.300Z", "files": {"output.json": {"content": "{\n  \"jobId\": 8,\n  \"success\": true,\n  \"result\": {\n    \"sum\": 11,\n    \"average\": 5.5,\n    \"max\": 6,\n    \"min\": 5,\n    \"count\": 2\n  },\n  \"message\": \"Data processing completed successfully. Sum: 11\",\n  \"timestamp\": \"2025-08-12T07:18:49.107Z\"\n}", "size": 239, "encoding": "utf8"}, "stdout.log": {"content": "Starting data processing job...\nInput data: { jobId: 8, params: { numbers: '5,6' } }\nData processing completed successfully\nResult: {\n  jobId: 8,\n  success: true,\n  result: { sum: 11, average: 5.5, max: 6, min: 5, count: 2 },\n  message: 'Data processing completed successfully. Sum: 11',\n  timestamp: '2025-08-12T07:18:49.107Z'\n}\n", "size": 330, "encoding": "utf8"}, "stderr.log": {"content": "", "size": 0, "encoding": "utf8"}, "result.hash": {"content": "7d2339f5b833071d977de8d5224bd5246b3233947b39db921fc307dc871a76f2", "size": 64, "encoding": "utf8"}, "signature.txt": {"content": "{\n  \"jobId\": 8,\n  \"resultHash\": \"7d2339f5b833071d977de8d5224bd5246b3233947b39db921fc307dc871a76f2\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"message\": \"DATACOIN Job Result\\nJob ID: 8\\nHash: 7d2339f5b833071d977de8d5224bd5246b3233947b39db921fc307dc871a76f2\\nProvider: 0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"signature\": \"0xb3b539e6e389604469b10e036c73c8c55df543471e38173de2218905b7ef4d7b3a69cd4eee8ad2b802fe2460a88539d9225dd58021cf61d1a53eb5ff9b13f3491b\",\n  \"timestamp\": \"2025-08-12T07:18:49.270Z\",\n  \"verification\": {\n    \"messageHash\": \"0x413cedc7be10b51a5765a17244ca088aaca6a0fe7d0e37c87e4857e5c25dfe19\",\n    \"recoveredAddress\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\"\n  }\n}", "size": 710, "encoding": "utf8"}, "metadata.json": {"content": "{\n  \"jobId\": 8,\n  \"timestamp\": \"2025-08-12T07:18:49.257Z\",\n  \"provider\": \"0x435145c61b0C80D83c44130C41Ff34DA2188b2df\",\n  \"files\": [\n    {\n      \"name\": \"output.json\",\n      \"size\": 239,\n      \"hash\": \"8d8da87d8bae523b9e0162d08e365f127c441a827193c82ecd1cc31bed451349\"\n    },\n    {\n      \"name\": \"stdout.log\",\n      \"size\": 330,\n      \"hash\": \"e40c18b3c4b55bd8d8a9f27bceb5297bfb92e3eeccdbdcaa7a2ccd8c9a5640fd\"\n    },\n    {\n      \"name\": \"stderr.log\",\n      \"size\": 0,\n      \"hash\": \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\"\n    }\n  ]\n}", "size": 558, "encoding": "utf8"}}, "manifest": {"version": "1.0", "jobId": 8, "createdAt": "2025-08-12T07:18:49.300Z", "fileCount": 6, "totalSize": 1901}}