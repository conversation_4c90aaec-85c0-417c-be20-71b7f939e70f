const { ethers } = require('ethers');
const fs = require('fs');
const path = require('path');
const IPFSUploader = require('./ipfs-uploader');

// Load environment variables
require('dotenv').config();

const RPC_URL = process.env.RPC_URL || "http://localhost:8545";
const CHECKER_PRIVATE_KEY = process.env.CHECKER_PRIVATE_KEY || '0x2a871d0798f97d79848a013d4936a73bf4cc922c825d33c1cf7073dff6d409c6';
const JOB_PAYMENT_ADDRESS = process.env.JOB_PAYMENT_ADDRESS;

// Load contract ABI
const JOB_PAYMENT_ABI = JSON.parse(fs.readFileSync(path.join(__dirname, '../artifacts/contracts/JobPayment.sol/JobPayment.json'))).abi;

class CheckerNode {
  constructor() {
    this.provider = new ethers.JsonRpcProvider(RPC_URL);
    this.wallet = new ethers.Wallet(CHECKER_PRIVATE_KEY, this.provider);
    this.contract = new ethers.Contract(JOB_PAYMENT_ADDRESS, JOB_PAYMENT_ABI, this.wallet);
    this.ipfsUploader = new IPFSUploader();
    
    this.checkedJobs = new Set();
    this.checkDataDir = path.join(__dirname, 'checker-data');
    
    // Ensure checker data directory exists
    if (!fs.existsSync(this.checkDataDir)) {
      fs.mkdirSync(this.checkDataDir, { recursive: true });
    }
    
    console.log(`🔍 Checker Node initialized`);
    console.log(`🔑 Checker address: ${this.wallet.address}`);
    console.log(`📋 Contract address: ${JOB_PAYMENT_ADDRESS}`);
  }

  /**
   * Start listening for JobCompleted events
   */
  async start() {
    console.log('🚀 Starting Checker Node...\n');
    
    try {
      // Listen for new JobCompleted events
      this.contract.on("JobCompleted", async (jobId, provider, success, proofCID, resultHash, event) => {
        console.log(`\n📡 JobCompleted Event Received:`);
        console.log(`   🆔 Job ID: ${jobId}`);
        console.log(`   👤 Provider: ${provider}`);
        console.log(`   ✅ Success: ${success}`);
        console.log(`   📤 Proof CID: ${proofCID}`);
        console.log(`   🔐 Result Hash: ${resultHash}`);

        await this.handleJobCompleted(jobId, success, event);
      });
      
      // Check for existing completed jobs
      await this.checkExistingJobs();
      
      console.log('👂 Listening for JobCompleted events...');
      console.log('🔗 Connected to blockchain\n');
      
    } catch (error) {
      console.error('❌ Failed to start Checker Node:', error);
      throw error;
    }
  }

  /**
   * Check existing completed jobs that haven't been verified
   */
  async checkExistingJobs() {
    console.log('🔍 Checking existing completed jobs...');
    
    try {
      // Get current job count
      const nextJobId = await this.contract.nextJobId();
      
      for (let jobId = 1; jobId < nextJobId; jobId++) {
        if (this.checkedJobs.has(jobId.toString())) {
          continue;
        }
        
        const job = await this.contract.jobs(jobId);
        
        // Check if job is completed (status = 2)
        if (job.status === 2n) {
          console.log(`📋 Found completed job ${jobId}, checking...`);
          await this.verifyJob(jobId, job);
        }
      }
      
    } catch (error) {
      console.error('❌ Error checking existing jobs:', error);
    }
  }

  /**
   * Handle JobCompleted event
   */
  async handleJobCompleted(jobId, success, event) {
    const jobIdStr = jobId.toString();
    
    if (this.checkedJobs.has(jobIdStr)) {
      console.log(`⚠️ Job ${jobId} already checked, skipping...`);
      return;
    }
    
    console.log(`\n📡 JobCompleted Event Received:`);
    console.log(`   🆔 Job ID: ${jobId}`);
    console.log(`   ✅ Success: ${success}`);
    console.log(`   📦 Block: ${event.blockNumber}`);
    console.log(`   🔗 Tx Hash: ${event.transactionHash}`);
    
    try {
      // Get full job details
      const job = await this.contract.jobs(jobId);
      await this.verifyJob(jobId, job);
      
    } catch (error) {
      console.error(`❌ Error handling JobCompleted for job ${jobId}:`, error);
    }
  }

  /**
   * Verify a completed job
   */
  async verifyJob(jobId, job) {
    const jobIdStr = jobId.toString();
    
    console.log(`\n🔍 VERIFYING JOB ${jobId}`);
    console.log('=' * 50);
    
    try {
      // Mark as being checked
      this.checkedJobs.add(jobIdStr);
      
      // Create job verification directory
      const jobCheckDir = path.join(this.checkDataDir, jobIdStr);
      if (!fs.existsSync(jobCheckDir)) {
        fs.mkdirSync(jobCheckDir, { recursive: true });
      }
      
      // Extract job information
      const jobInfo = {
        id: job.id.toString(),
        requester: job.requester,
        provider: job.provider,
        jobType: job.jobType,
        status: job.status.toString(),
        result: job.result,
        createdAt: new Date(Number(job.createdAt) * 1000).toISOString(),
        completedAt: new Date(Number(job.completedAt) * 1000).toISOString()
      };
      
      console.log('📋 Job Information:');
      console.log(`   🆔 ID: ${jobInfo.id}`);
      console.log(`   👤 Provider: ${jobInfo.provider}`);
      console.log(`   📝 Type: ${jobInfo.jobType}`);
      console.log(`   📊 Status: ${jobInfo.status}`);
      console.log(`   📝 Result: ${jobInfo.result}`);
      
      // Save job info
      fs.writeFileSync(
        path.join(jobCheckDir, 'job_info.json'),
        JSON.stringify(jobInfo, null, 2)
      );
      
      // Parse result to extract proof data
      let proofData = null;
      try {
        const parsedResult = JSON.parse(jobInfo.result);
        if (parsedResult.proofCID && parsedResult.resultHash) {
          proofData = {
            proofCID: parsedResult.proofCID,
            resultHash: parsedResult.resultHash,
            baseResult: parsedResult.result
          };
        }
      } catch (error) {
        console.log('   📝 Result format: Plain text (no embedded proof)');
      }
      
      if (proofData) {
        console.log('\n📤 Proof Data Found:');
        console.log(`   📤 IPFS CID: ${proofData.proofCID}`);
        console.log(`   🔐 Result Hash: ${proofData.resultHash}`);
        
        // Proceed with full verification
        const verification = await this.performFullVerification(jobId, jobInfo, proofData, jobCheckDir);
        
        // Save verification result
        fs.writeFileSync(
          path.join(jobCheckDir, 'verification_result.json'),
          JSON.stringify(verification, null, 2)
        );
        
        console.log(`\n🎯 VERIFICATION RESULT: ${verification.overall ? 'PASSED ✅' : 'FAILED ❌'}`);
        
      } else {
        console.log('\n⚠️ No proof data found in result - basic verification only');
        
        const basicVerification = {
          jobId: jobIdStr,
          timestamp: new Date().toISOString(),
          hasProofData: false,
          basicChecks: {
            hasResult: !!jobInfo.result,
            validProvider: ethers.isAddress(jobInfo.provider),
            validTimestamp: jobInfo.completedAt > jobInfo.createdAt
          }
        };
        
        fs.writeFileSync(
          path.join(jobCheckDir, 'verification_result.json'),
          JSON.stringify(basicVerification, null, 2)
        );
        
        console.log('✅ Basic verification completed');
      }
      
    } catch (error) {
      console.error(`❌ Error verifying job ${jobId}:`, error);
      
      // Save error info
      const errorInfo = {
        jobId: jobIdStr,
        timestamp: new Date().toISOString(),
        error: error.message,
        stack: error.stack
      };
      
      const jobCheckDir = path.join(this.checkDataDir, jobIdStr);
      if (!fs.existsSync(jobCheckDir)) {
        fs.mkdirSync(jobCheckDir, { recursive: true });
      }
      
      fs.writeFileSync(
        path.join(jobCheckDir, 'verification_error.json'),
        JSON.stringify(errorInfo, null, 2)
      );
    }
  }

  /**
   * Perform full verification with IPFS artifacts
   */
  async performFullVerification(jobId, jobInfo, proofData, jobCheckDir) {
    console.log('\n🔍 Performing full verification...');
    
    const verification = {
      jobId: jobId.toString(),
      timestamp: new Date().toISOString(),
      hasProofData: true,
      ipfsDownload: false,
      hashVerification: false,
      signatureVerification: false,
      qualityChecks: {},
      overall: false
    };
    
    try {
      // Step 1: Download from IPFS
      console.log('📥 Step 1: Downloading artifacts from IPFS...');
      const artifacts = await this.ipfsUploader.downloadArtifact(proofData.proofCID);
      
      if (artifacts) {
        verification.ipfsDownload = true;
        console.log(`   ✅ Downloaded ${artifacts.manifest.fileCount} files`);
        
        // Save downloaded artifacts info
        fs.writeFileSync(
          path.join(jobCheckDir, 'downloaded_artifacts.json'),
          JSON.stringify(artifacts, null, 2)
        );
        
        // Step 2: Verify hash
        console.log('🔐 Step 2: Verifying result hash...');
        verification.hashVerification = await this.verifyHash(artifacts, proofData.resultHash);
        
        // Step 3: Verify signature
        console.log('✍️ Step 3: Verifying digital signature...');
        verification.signatureVerification = await this.verifySignature(artifacts, jobInfo.provider);
        
        // Step 4: Quality checks
        console.log('🔍 Step 4: Performing quality checks...');
        verification.qualityChecks = await this.performQualityChecks(artifacts, jobInfo);
        
      } else {
        console.log('   ❌ Failed to download artifacts');
      }
      
    } catch (error) {
      console.error('❌ Verification error:', error.message);
      verification.error = error.message;
    }
    
    // Overall result
    verification.overall = verification.ipfsDownload && 
                          verification.hashVerification && 
                          verification.signatureVerification &&
                          Object.values(verification.qualityChecks).every(check => check);
    
    return verification;
  }

  /**
   * Verify hash integrity
   */
  async verifyHash(artifacts, expectedHash) {
    try {
      // Find hash file in artifacts (files is an object, not array)
      const hashFile = artifacts.files['result.hash'];
      if (!hashFile) {
        console.log('   ❌ No hash file found');
        return false;
      }

      const storedHash = hashFile.content.trim();
      const hashMatch = storedHash === expectedHash;

      console.log(`   📊 Stored hash: ${storedHash}`);
      console.log(`   📊 Expected hash: ${expectedHash}`);
      console.log(`   ${hashMatch ? '✅' : '❌'} Hash verification: ${hashMatch ? 'PASSED' : 'FAILED'}`);

      return hashMatch;

    } catch (error) {
      console.log(`   ❌ Hash verification error: ${error.message}`);
      return false;
    }
  }

  /**
   * Verify digital signature
   */
  async verifySignature(artifacts, expectedProvider) {
    try {
      // Find signature file (files is an object, not array)
      const signatureFile = artifacts.files['signature.txt'];
      if (!signatureFile) {
        console.log('   ❌ No signature file found');
        return false;
      }

      const signatureData = JSON.parse(signatureFile.content);
      const recoveredAddress = ethers.verifyMessage(signatureData.message, signatureData.signature);
      const signatureValid = recoveredAddress.toLowerCase() === expectedProvider.toLowerCase();

      console.log(`   🔑 Expected provider: ${expectedProvider}`);
      console.log(`   🔑 Recovered address: ${recoveredAddress}`);
      console.log(`   ${signatureValid ? '✅' : '❌'} Signature verification: ${signatureValid ? 'PASSED' : 'FAILED'}`);

      return signatureValid;

    } catch (error) {
      console.log(`   ❌ Signature verification error: ${error.message}`);
      return false;
    }
  }

  /**
   * Perform basic quality checks
   */
  async performQualityChecks(artifacts, jobInfo) {
    const checks = {};

    try {
      // Check if output.json exists and is valid (files is an object, not array)
      const outputFile = artifacts.files['output.json'];
      if (outputFile) {
        try {
          const output = JSON.parse(outputFile.content);
          checks.validOutput = output.success === true;
          checks.hasResult = !!output.result;
          checks.reasonableExecutionTime = true; // Basic check

          console.log(`   ${checks.validOutput ? '✅' : '❌'} Valid output format`);
          console.log(`   ${checks.hasResult ? '✅' : '❌'} Has result data`);

        } catch (error) {
          checks.validOutput = false;
          checks.hasResult = false;
          console.log('   ❌ Invalid output.json format');
        }
      } else {
        checks.validOutput = false;
        checks.hasResult = false;
        console.log('   ❌ No output.json found');
      }

      // Check for errors in stderr (files is an object, not array)
      const stderrFile = artifacts.files['stderr.log'];
      if (stderrFile) {
        checks.noErrors = stderrFile.content.trim().length === 0;
        console.log(`   ${checks.noErrors ? '✅' : '⚠️'} No stderr errors`);
      } else {
        checks.noErrors = true;
      }

      // Check execution time reasonableness (basic)
      const createdAt = new Date(jobInfo.createdAt);
      const completedAt = new Date(jobInfo.completedAt);
      const executionTime = completedAt - createdAt;

      checks.reasonableExecutionTime = executionTime > 0 && executionTime < 10 * 60 * 1000; // < 10 minutes
      console.log(`   ${checks.reasonableExecutionTime ? '✅' : '⚠️'} Reasonable execution time (${Math.round(executionTime/1000)}s)`);

    } catch (error) {
      console.log(`   ❌ Quality check error: ${error.message}`);
      checks.error = error.message;
    }

    return checks;
  }
}

// Main execution
async function main() {
  const checker = new CheckerNode();
  await checker.start();
  
  // Keep the process running
  process.on('SIGINT', () => {
    console.log('\n👋 Checker Node shutting down...');
    process.exit(0);
  });
}

// Export for use as module
module.exports = CheckerNode;

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
