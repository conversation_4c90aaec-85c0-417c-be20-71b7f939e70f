#!/bin/bash

# Build Docker images for job types

echo "🔨 Building Docker images for DATACOIN job processing..."

# Build data_processing image
echo "📦 Building data_processing image..."
cd docker/data_processing
sudo docker build -t datacoin-data_processing:latest .
if [ $? -eq 0 ]; then
    echo "✅ Successfully built datacoin-data_processing:latest"
else
    echo "❌ Failed to build datacoin-data_processing:latest"
    exit 1
fi
cd ../..

# Build ai_training image
echo "📦 Building ai_training image..."
cd docker/ai_training
sudo docker build -t datacoin-ai_training:latest .
if [ $? -eq 0 ]; then
    echo "✅ Successfully built datacoin-ai_training:latest"
else
    echo "❌ Failed to build datacoin-ai_training:latest"
    exit 1
fi
cd ../..

echo "🎉 All Docker images built successfully!"

# List built images
echo "📋 Built images:"
sudo docker images | grep datacoin-
