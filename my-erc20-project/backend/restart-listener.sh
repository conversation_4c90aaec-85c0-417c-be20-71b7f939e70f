#!/bin/bash

echo "🔄 Restarting job-listener with improved error handling..."

# Kill existing job-listener processes
echo "🛑 Stopping existing job-listener processes..."
pkill -f "node job-listener.js" || echo "No existing processes found"

# Wait a moment
sleep 2

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️ Warning: .env file not found. Make sure environment variables are set."
fi

# Start job-listener with nohup to run in background
echo "🚀 Starting job-listener..."
nohup node job-listener.js > job-listener.log 2>&1 &

# Get the PID
PID=$!
echo "✅ Job-listener started with PID: $PID"

# Wait a moment and check if it's still running
sleep 3
if ps -p $PID > /dev/null; then
    echo "✅ Job-listener is running successfully"
    echo "📋 To view logs: tail -f job-listener.log"
    echo "🛑 To stop: kill $PID"
else
    echo "❌ Job-listener failed to start. Check job-listener.log for errors"
    cat job-listener.log
fi
