// SPDX-License-Identifier: MIT
pragma solidity ^0.8.17;
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

contract DockerJobPayment is Ownable, ReentrancyGuard {
    struct JobType {
        string image;
        bool exists;
    }

    enum JobStatus { Created, Assigned, Running, Completed, Cancelled, Disputed }

    struct Job {
        uint256 id;
        address requester;
        address provider;
        string jobType;
        uint256 cpu;
        uint256 mem;
        uint256 gpu;
        uint256 totalPayment;
        uint256 paidToProvider;
        JobStatus status;
        string resultHash;
        string params;
        uint256 createdAt;
        uint256 updatedAt;
    }

    mapping(string => JobType) public jobTypes;
    string[] public jobTypeList;

    mapping(uint256 => Job) public jobs;
    uint256 public jobCount;

    function nextJobId() external view returns (uint256) {
        return jobCount + 1;
    }

    modifier onlyRequester(uint256 jobId) {
        require(msg.sender == jobs[jobId].requester, "Only requester");
        _;
    }

    modifier onlyProvider(uint256 jobId) {
        require(msg.sender == jobs[jobId].provider, "Only provider");
        _;
    }

    event JobTypeSet(string indexed jobType, string image);
    event JobRequested(uint256 indexed jobId, address indexed requester, string jobType, uint256 payment);
    event JobAssigned(uint256 indexed jobId, address indexed provider);
    event JobStarted(uint256 indexed jobId);
    event JobPartialPaid(uint256 indexed jobId, address indexed provider, uint256 amount);
    event JobCompleted(uint256 indexed jobId, string resultHash);
    event JobRefunded(uint256 indexed jobId, uint256 amount);

    constructor(address _owner) {
        _transferOwnership(_owner);
    }

    function setJobType(string calldata jobType, string calldata image) external onlyOwner {
        if (!jobTypes[jobType].exists) {
            jobTypeList.push(jobType);
            jobTypes[jobType].exists = true;
        }
        jobTypes[jobType].image = image;
        emit JobTypeSet(jobType, image);
    }

    function requestJob(
        string calldata jobType,
        uint256 cpu,
        uint256 mem,
        uint256 gpu,
        string calldata params
    ) external payable {
        require(jobTypes[jobType].exists, "Job type not found");
        require(msg.value > 0, "Must send payment");

        jobCount++;
        jobs[jobCount] = Job({
            id: jobCount,
            requester: msg.sender,
            provider: address(0),
            jobType: jobType,
            cpu: cpu,
            mem: mem,
            gpu: gpu,
            totalPayment: msg.value,
            paidToProvider: 0,
            status: JobStatus.Created,
            resultHash: "",
            params: params,
            createdAt: block.timestamp,
            updatedAt: block.timestamp
        });

        emit JobRequested(jobCount, msg.sender, jobType, msg.value);
    }

    function assignJob(uint256 jobId) external {
        Job storage job = jobs[jobId];
        require(job.status == JobStatus.Created, "Job not available");
        job.provider = msg.sender;
        job.status = JobStatus.Assigned;
        job.updatedAt = block.timestamp;

        emit JobAssigned(jobId, msg.sender);
    }

    function startJob(uint256 jobId) external onlyProvider(jobId) {
        Job storage job = jobs[jobId];
        require(job.status == JobStatus.Assigned, "Job not assigned");
        job.status = JobStatus.Running;
        job.updatedAt = block.timestamp;

        emit JobStarted(jobId);
    }

    function partialPayment(uint256 jobId, uint256 amount) external onlyRequester(jobId) {
        Job storage job = jobs[jobId];
        require(job.status == JobStatus.Running || job.status == JobStatus.Assigned, "Job not running/assigned");
        require(amount > 0 && amount <= job.totalPayment - job.paidToProvider, "Invalid amount");

        job.paidToProvider += amount;
        payable(job.provider).transfer(amount);
        job.updatedAt = block.timestamp;

        emit JobPartialPaid(jobId, job.provider, amount);
    }

    function completeJob(uint256 jobId, string calldata resultHash) external onlyProvider(jobId) {
        Job storage job = jobs[jobId];
        require(job.status == JobStatus.Running, "Job not running");
        job.status = JobStatus.Completed;
        job.resultHash = resultHash;
        job.updatedAt = block.timestamp;

        uint256 remain = job.totalPayment - job.paidToProvider;
        if (remain > 0) {
            job.paidToProvider += remain;
            payable(job.provider).transfer(remain);
        }

        emit JobCompleted(jobId, resultHash);
    }

    function refundPayment(uint256 jobId) external onlyRequester(jobId) {
        Job storage job = jobs[jobId];
        require(job.status == JobStatus.Created || job.status == JobStatus.Assigned, "Can't refund now");
        uint256 remain = job.totalPayment - job.paidToProvider;
        require(remain > 0, "No funds to refund");

        job.status = JobStatus.Cancelled;
        job.updatedAt = block.timestamp;

        payable(job.requester).transfer(remain);

        emit JobRefunded(jobId, remain);
    }

    // View functions
    function getJobDetails(uint256 jobId) external view returns (
        uint256 id,
        address requester,
        address provider,
        string memory jobType,
        uint256 cpu,
        uint256 mem,
        uint256 gpu,
        uint256 totalPayment,
        uint256 paidToProvider,
        JobStatus status,
        string memory resultHash,
        string memory params,
        uint256 createdAt,
        uint256 updatedAt
    ) {
        Job storage job = jobs[jobId];
        return (
            job.id,
            job.requester,
            job.provider,
            job.jobType,
            job.cpu,
            job.mem,
            job.gpu,
            job.totalPayment,
            job.paidToProvider,
            job.status,
            job.resultHash,
            job.params,
            job.createdAt,
            job.updatedAt
        );
    }

    function getJobTypes() external view returns (string[] memory) {
        return jobTypeList;
    }
}
