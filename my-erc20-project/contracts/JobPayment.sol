// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

contract JobPayment is Ownable, ReentrancyGuard {
    enum JobStatus { Pending, Running, Completed, Failed, Cancelled }

    struct Job {
        uint256 id;
        address requester;
        address provider;
        string jobType;
        uint256 payment;
        JobStatus status;
        uint256 createdAt;
        uint256 completedAt;
        string params;
        string result;
        string proofCID;        // IPFS CID of proof artifacts
        string resultHash;      // SHA256 hash of result
    }

    mapping(uint256 => Job) public jobs;
    mapping(string => uint256) public jobTypePrices; // jobType => native token amount

    uint256 public nextJobId = 1;
    uint256 public platformFeePercent = 5; // 5%

    event JobRequested(uint256 indexed jobId, address indexed requester, string jobType, uint256 payment);
    event JobAssigned(uint256 indexed jobId, address indexed provider);
    event JobCompleted(uint256 indexed jobId, address indexed provider, bool success, string proofCID, string resultHash);
    event PaymentSettled(uint256 indexed jobId, address indexed provider, uint256 amount);

    constructor(address _owner) {
        _transferOwnership(_owner);
    }

    function setJobTypePrice(string memory jobType, uint256 price) external onlyOwner {
        jobTypePrices[jobType] = price;
    }

    function requestJob(string memory jobType, string memory params) external payable {
        uint256 requiredPayment = jobTypePrices[jobType];
        require(requiredPayment > 0, "Job type not supported");
        require(msg.value >= requiredPayment, "Insufficient native token payment");

        jobs[nextJobId] = Job({
            id: nextJobId,
            requester: msg.sender,
            provider: address(0),
            jobType: jobType,
            payment: requiredPayment,
            status: JobStatus.Pending,
            createdAt: block.timestamp,
            completedAt: 0,
            params: params,
            result: "",
            proofCID: "",
            resultHash: ""
        });

        emit JobRequested(nextJobId, msg.sender, jobType, requiredPayment);
        nextJobId++;
    }

    function getJobResult(uint256 jobId) external view returns (string memory) {
        return jobs[jobId].result;
    }

    function getJobProof(uint256 jobId) external view returns (string memory proofCID, string memory resultHash) {
        return (jobs[jobId].proofCID, jobs[jobId].resultHash);
    }

    function getJobDetails(uint256 jobId) external view returns (
        uint256 id,
        address requester,
        address provider,
        string memory jobType,
        uint256 payment,
        JobStatus status,
        uint256 createdAt,
        uint256 completedAt,
        string memory params,
        string memory result,
        string memory proofCID,
        string memory resultHash
    ) {
        Job memory job = jobs[jobId];
        return (
            job.id,
            job.requester,
            job.provider,
            job.jobType,
            job.payment,
            job.status,
            job.createdAt,
            job.completedAt,
            job.params,
            job.result,
            job.proofCID,
            job.resultHash
        );
    }

    function assignJob(uint256 jobId, address provider) external {
        require(provider != address(0), "Invalid provider address");
        require(jobs[jobId].status == JobStatus.Pending, "Job is not pending");
        jobs[jobId].provider = provider;
        jobs[jobId].status = JobStatus.Running;
        emit JobAssigned(jobId, provider);
    }

    function completeJob(uint256 jobId, bool success, string memory result) external {
        require(jobs[jobId].status == JobStatus.Running, "Job not running");
        require(jobs[jobId].provider == msg.sender, "Only assigned provider can complete the job");

        jobs[jobId].status = success ? JobStatus.Completed : JobStatus.Failed;
        jobs[jobId].completedAt = block.timestamp;
        jobs[jobId].result = result;

        emit JobCompleted(jobId, msg.sender, success, "", "");

        if (success) {
            settlePayment(jobId);
        } else {
            refundPayment(jobId);
        }
    }

    function completeJobWithProof(
        uint256 jobId,
        bool success,
        string memory result,
        string memory proofCID,
        string memory resultHash
    ) external {
        require(jobs[jobId].status == JobStatus.Running, "Job not running");
        require(jobs[jobId].provider == msg.sender, "Only assigned provider can complete the job");

        jobs[jobId].status = success ? JobStatus.Completed : JobStatus.Failed;
        jobs[jobId].completedAt = block.timestamp;
        jobs[jobId].result = result;
        jobs[jobId].proofCID = proofCID;
        jobs[jobId].resultHash = resultHash;

        emit JobCompleted(jobId, msg.sender, success, proofCID, resultHash);

        if (success) {
            settlePayment(jobId);
        } else {
            refundPayment(jobId);
        }
    }

    function settlePayment(uint256 jobId) internal {
        Job storage job = jobs[jobId];
        require(job.status == JobStatus.Completed, "Job not completed");

        uint256 platformFee = (job.payment * platformFeePercent) / 100;
        uint256 providerPayment = job.payment - platformFee;

        payable(job.provider).transfer(providerPayment);
        payable(owner()).transfer(platformFee);

        emit PaymentSettled(jobId, job.provider, providerPayment);
    }

    function refundPayment(uint256 jobId) internal {
        Job storage job = jobs[jobId];
        payable(job.requester).transfer(job.payment);
    }
}