require('dotenv').config();
const { ethers } = require("hardhat");

async function main() {
  // Địa chỉ JobPayment contract đã deploy
  const jobPaymentAddress = process.env.JOB_PAYMENT_ADDRESS;

  const JobPayment = await ethers.getContractAt("JobPayment", jobPaymentAddress);

  // Danh sách job mới mở rộng theo tài liệu
  const jobTypes = [
    { type: "data_processing", price: ethers.parseEther("10") },        // 10 DATACOIN
    { type: "ai_training", price: ethers.parseEther("50") },            // 50 DATACOIN
    { type: "image_analysis", price: ethers.parseEther("20") },         // 20 DATACOIN
    { type: "text_analysis", price: ethers.parseEther("15") },          // 15 DATACOIN
    { type: "text_generation", price: ethers.parseEther("25") },        // 25 DATACOIN
    { type: "image_generation", price: ethers.parseEther("30") },       // 30 DATACOIN
    { type: "speech_to_text", price: ethers.parseEther("18") },         // 18 DATACOIN
    { type: "fact_checking", price: ethers.parseEther("22") },          // 22 DATACOIN
    { type: "fine_tuning", price: ethers.parseEther("100") },           // 100 DATACOIN
    { type: "gpu_benchmark", price: ethers.parseEther("5") }            // 5 DATACOIN
  ];

  for (const job of jobTypes) {
    console.log(`🛠️ Setting price for ${job.type}: ${ethers.formatEther(job.price)} DATACOIN`);
    const tx = await JobPayment.setJobTypePrice(job.type, job.price);
    await tx.wait();
    console.log(`✅ Set ${job.type} price`);
  }

  console.log("✅ All job types configured!");
}

main().catch((error) => {
  console.error("❌ Error setting job prices:", error);
  process.exitCode = 1;
});

//npx hardhat run scripts/setup-job-types.js --network ethermint
