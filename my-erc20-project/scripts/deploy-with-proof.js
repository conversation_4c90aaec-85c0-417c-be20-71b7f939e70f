const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying DATACOIN contracts with proof support...\n");

  // Get deployer account
  const [deployer] = await ethers.getSigners();
  console.log("📝 Deploying contracts with account:", deployer.address);
  console.log("💰 Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH\n");

  // Deploy DataCoin token
  console.log("📦 Deploying DataCoin token...");
  const DataCoin = await ethers.getContractFactory("DataCoin");
  const dataCoin = await DataCoin.deploy();
  await dataCoin.waitForDeployment();
  
  const dataCoinAddress = await dataCoin.getAddress();
  console.log("✅ DataCoin deployed to:", dataCoinAddress);

  // Deploy JobPayment contract with proof support
  console.log("\n📦 Deploying JobPayment contract with proof support...");
  const JobPayment = await ethers.getContractFactory("JobPayment");
  const jobPayment = await JobPayment.deploy(dataCoinAddress);
  await jobPayment.waitForDeployment();
  
  const jobPaymentAddress = await jobPayment.getAddress();
  console.log("✅ JobPayment deployed to:", jobPaymentAddress);

  // Setup job types
  console.log("\n⚙️ Setting up job types...");
  
  const jobTypes = [
    { name: "data_processing", payment: ethers.parseEther("10") },
    { name: "ai_training", payment: ethers.parseEther("20") }
  ];

  for (const jobType of jobTypes) {
    const tx = await jobPayment.setJobTypePayment(jobType.name, jobType.payment);
    await tx.wait();
    console.log(`✅ Set ${jobType.name}: ${ethers.formatEther(jobType.payment)} DATACOIN`);
  }

  // Mint tokens for testing
  console.log("\n💰 Minting test tokens...");
  const mintAmount = ethers.parseEther("10000");
  const mintTx = await dataCoin.mint(deployer.address, mintAmount);
  await mintTx.wait();
  console.log(`✅ Minted ${ethers.formatEther(mintAmount)} DATACOIN to deployer`);

  // Display deployment summary
  console.log("\n" + "=".repeat(60));
  console.log("🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!");
  console.log("=".repeat(60));
  console.log("📋 Contract Addresses:");
  console.log(`   DataCoin Token: ${dataCoinAddress}`);
  console.log(`   JobPayment:     ${jobPaymentAddress}`);
  
  console.log("\n📋 New Features:");
  console.log("   ✅ completeJobWithProof() method");
  console.log("   ✅ proofCID and resultHash storage");
  console.log("   ✅ Enhanced JobCompleted event");
  console.log("   ✅ getJobProof() and getJobDetails() getters");
  
  console.log("\n📋 Job Types:");
  jobTypes.forEach(jobType => {
    console.log(`   ✅ ${jobType.name}: ${ethers.formatEther(jobType.payment)} DATACOIN`);
  });

  // Save addresses to file for backend
  const addresses = {
    DataCoin: dataCoinAddress,
    JobPayment: jobPaymentAddress,
    deployer: deployer.address,
    network: "localhost",
    deployedAt: new Date().toISOString(),
    features: {
      proofSupport: true,
      ipfsIntegration: true,
      resultAuthentication: true
    }
  };

  const fs = require('fs');
  const path = require('path');
  
  // Save to backend directory
  const backendDir = path.join(__dirname, '..', 'backend');
  const addressesFile = path.join(backendDir, 'contract-addresses.json');
  
  fs.writeFileSync(addressesFile, JSON.stringify(addresses, null, 2));
  console.log(`\n📁 Contract addresses saved to: ${addressesFile}`);

  // Update .env file
  const envFile = path.join(backendDir, '.env');
  let envContent = '';
  
  if (fs.existsSync(envFile)) {
    envContent = fs.readFileSync(envFile, 'utf8');
  }
  
  // Update or add contract addresses
  const envUpdates = [
    `JOB_PAYMENT_ADDRESS=${jobPaymentAddress}`,
    `DATACOIN_ADDRESS=${dataCoinAddress}`,
    `NETWORK=localhost`,
    `PROOF_SUPPORT=true`
  ];
  
  envUpdates.forEach(update => {
    const [key, value] = update.split('=');
    const regex = new RegExp(`^${key}=.*$`, 'm');
    
    if (envContent.match(regex)) {
      envContent = envContent.replace(regex, update);
    } else {
      envContent += `\n${update}`;
    }
  });
  
  fs.writeFileSync(envFile, envContent.trim() + '\n');
  console.log(`📁 Environment variables updated in: ${envFile}`);

  console.log("\n🚀 Ready for Giai đoạn 2 testing!");
  console.log("   1. Start job-listener: cd backend && node job-listener.js");
  console.log("   2. Create jobs with proof support");
  console.log("   3. Verify proof artifacts on-chain");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
