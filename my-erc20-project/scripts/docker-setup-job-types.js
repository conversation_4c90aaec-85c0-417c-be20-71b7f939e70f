require('dotenv').config();
const { ethers } = require("hardhat");

async function main() {
  const jobPaymentAddress = process.env.JOB_PAYMENT_ADDRESS;
  const DockerJobPayment = await ethers.getContractAt("DockerJobPayment", jobPaymentAddress);

  // Danh sách job types kèm image
  const jobTypes = [
    { type: "docker_add", image: "dockerimagestest_add:latest" },
    { type: "docker_sub", image: "dockerimagestest_sub:latest" },
    { type: "docker_mul", image: "dockerimagestest_mul:latest" },
    { type: "docker_div", image: "dockerimagestest_div:latest" },
  ];

  for (const job of jobTypes) {
    console.log(`🛠️ Setting image for ${job.type}: ${job.image}`);
    const tx = await DockerJobPayment.setJobType(job.type, job.image);
    await tx.wait();
    console.log(`✅ Set image for ${job.type}`);
  }

  console.log("✅ All job types configured!");
}

main().catch((error) => {
  console.error("❌ Error setting job images:", error);
  process.exitCode = 1;
});
