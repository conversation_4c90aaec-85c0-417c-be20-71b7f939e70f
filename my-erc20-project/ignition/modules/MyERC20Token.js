require('dotenv').config();
const { buildModule } = require("@nomicfoundation/hardhat-ignition/modules");

module.exports = buildModule("MyERC20TokenModule", (m) => {
  const tokenName = m.getParameter("tokenName", "DataCoinERC");
  const tokenSymbol = m.getParameter("tokenSymbol", "ERC");
  const initialSupply = m.getParameter("initialSupply", 1000000);
  const initialOwner = m.getParameter("initialOwner", process.env.OWNER_ADDRESS);
  const token = m.contract("MyERC20Token", [
    tokenName,
    tokenSymbol,
    initialSupply,
    initialOwner
  ]);

  return { token };
});