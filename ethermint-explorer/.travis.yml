language: node_js
node_js:
  - "0.10"

before_script:
  - export DISPLAY=:99.0
  - sh -e /etc/init.d/xvfb start
  - npm start > /dev/null &
  - npm run update-webdriver
  - sleep 1 # give server time to start

script:
  - node_modules/.bin/karma start karma.conf.js --no-auto-watch --single-run --reporters=dots --browsers=Firefox
  - node_modules/.bin/protractor e2e-tests/protractor.conf.js --browser=firefox
