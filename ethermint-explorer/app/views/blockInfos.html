<div class="container" style="padding-top: 50px;">

    <h1>Block
        <small>View information about an Ethereum Block</small>
    </h1>

    <div id="block-97785883" class="blockdiv">
        <table class="table table-striped" cellpadding="0" cellspacing="0" style="padding:0px;float:left;margin:0px;width:100%">
            <tbody>
            <tr>
                <th colspan="3" align="left"><a class="hash-link" href="./#/block/{{blockId}}">{{hash}}</a> <span class="pull-right"></span></th>
            </tr>
            </tbody>
        </table>
        <div style="padding-bottom:30px;width:100%;text-align:right;clear:both">
            <button class="btn btn-primary">{{conf}}</button>
            <button class="btn btn-success cb"><span>{{gasUsed}} Gas Used</span></button>
        </div>
        <table class="table table-striped">
            <tbody><tr>
                <th colspan="2" style="text-align:left">Summary</th>
            </tr>
            <tr>
                <td>Block Number</td>
                <td><a href="./#/block/{{blockNumber}}">{{number}}</a></td>
            </tr>
            <tr>
                <td>Received Time</td>
                <td>
                    {{timestamp}}
                </td>
            </tr>
            <tr>
                <td>Difficulty</td>
                <td>
                    {{difficulty}}
                </td>
            </tr>
            <tr>
                <td>Nonce</td>
                <td>{{nonce}}</td>
            </tr>
            <tr>
                <td>Size</td>
                <td>{{size}}</td>
            </tr>
            <tr>
                <td>Miner</td>
                <td><a href="./#/address/{{miner}}">{{miner}}</a></td>
            </tr>
            <tr>
                <td>Gas Limit</td>
                <td>{{gasLimit}}</td>
            </tr>
            <tr>
                <td>Data</td>
                <td>{{extraData}}</td>
            </tr>

            <tr>
                <td>Data (Translated)</td>
                <td>{{dataFromHex}}</td>
            </tr>

            </tbody>
        </table>
    </div>

    <h2 style="margin-top: 30px;">
      Transactions
      <small>- contained in current block</small>
    </h2>

    <div ng-repeat="tx in transactions">
      <table class="table table-striped">
          <tbody>
          <tr>
              <th colspan="2" style="text-align:left">Transaction #{{$index+1}}</th>
          </tr>
          <tr>
              <td>Hash #</td>
              <td><a href="./#/transaction/{{tx.hash}}">{{tx.hash}}</a></td>
          </tr>
          <tr>
              <td>From</td>
              <td><a href="./#/address/{{tx.from}}">{{tx.from}}</a></td>
          </tr>
          <tr>
              <td>To</td>
              <td><a href="./#/address/{{tx.to}}">{{tx.to}}</a></td>
          </tr>
          <tr>
              <td>Gas</td>
              <td>{{tx.gas}}</a></td>
          </tr>
          <tr>
              <td>Input</td>
              <td>{{tx.input}}</a></td>
          </tr>
          <tr>
              <td>Value</td>
              <td>{{tx.value}}</a></td>
          </tr>
          </tbody>
      </table>
      <hr>
    </div>

</div>
